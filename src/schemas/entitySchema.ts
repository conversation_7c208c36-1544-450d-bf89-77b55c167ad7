import { isEmptyField } from '@/utils';
import { z } from 'zod';

export const EntityInputSchema = z.object({
  name: z.string().nonempty({ message: 'forms.errors.name.required' }),
  email: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'forms.errors.email.invalidFormat',
    }),
  phone: z
    .string()
    .optional()
    .refine((val) => isEmptyField(val) || (typeof val === 'string' && val.length >= 9), {
      message: 'forms.errors.phone.wrongFormat',
    }),
  nif: z
    .string()
    .optional()
    .refine((val) => isEmptyField(val) || val?.length === 9, {
      message: 'forms.errors.nif.wrongLength',
    }),
  website: z.string().optional(),
  location: z.string().optional(),
  distrito: z.string().optional(),
  concelho: z.string().optional(),
  freguesia: z.string().optional(),
  capital_social: z.string().optional(),
  country: z.literal('PT').optional().default('PT'),
  type: z
    .enum(['cliente_formacao', 'cliente_faturacao', 'entidade_empregadora', 'parceria'])
    .optional(),
  rgpd: z
    .object({
      consent_1: z.boolean().default(false),
      consent_2: z.boolean().default(false),
      consent_3: z.boolean().default(false),
      consent_4: z.boolean().default(false),
      consent_5: z.boolean().default(false),
    })
    .optional()
    .default({}),
  observations: z.string().optional(),
  protocols: z
    .array(z.enum(['praticas', 'estagios']))
    .max(2)
    .optional()
    .default([]),
});
export type EntityInput = z.infer<typeof EntityInputSchema>;

export type Entity = EntityInput & {
  id: string;
};
