import z from 'zod';
import { mapLabel } from '../utils';
import { useOptionsIntl } from '../hooks/useFieldOptionsIntl';

export const opportunityInputSchema = z.object({
  description: z.string(),
  contact: z.string().optional(),
  type: z.string().optional(),
  entity: z.string().optional(),
  assignedTo: z.string().optional(),
  observations: z.string().default(''),
  course: z.string().optional(),
  center: z.string().optional(),
  modality: z.string().optional(),
  shift: z.string().optional(),
  baseEducation: z.string().optional(),
  baseType9: z.string().optional(),
  baseType12: z.string().optional(),
});

export type OpportunityInput = z.infer<typeof opportunityInputSchema>;

export const opportunityStates = [
  'P2 + 3B',
  'P1',
  'Sucesso',
  'Insucesso',
  'F. Up - Quente',
  'F. Up - Morno',
  'F. Up - Frio',
] as const;

export const opportunityStateOptions = mapLabel(opportunityStates);

export type OpportunityState = (typeof opportunityStates)[number];

export const opportunityTypes = ['registration'] as const;

export const useOpportunityTypesOptions = () =>
  useOptionsIntl(opportunityTypes, 'forms.opportunityTypes');

export type OpportunityType = (typeof opportunityTypes)[number];

export const interactionTypes = ['call', 'sms', 'mail'] as const;
export const useInteractionTypesOptions = () =>
  useOptionsIntl(interactionTypes, 'forms.interactionTypes');

export type InteractionType = (typeof interactionTypes)[number];

export const interactionStates = ['success', 'insuccess'] as const;

export const useInteractionStateOptions = () => useOptionsIntl(interactionStates);

export type InteractionState = (typeof interactionStates)[number];

export const opportunityEditSchema = z.object({
  description: z.string(),
  contact: z.string().optional(),
  type: z.string().optional(),
  entity: z.string().optional(),
  assignedTo: z.string().optional(),
  state: z.enum(opportunityStates),
  stateDate: z.date(),
  closeDate: z.date(),
  resultState: z.enum(opportunityStates),
  resultReason: z.string(),
  resultDescription: z.string(),
  createdBy: z.string(),
  sourceLead: z.string(),

  observations: z.string().default(''),
  course: z.string().optional(),
  center: z.string().optional(),
  modality: z.string().optional(),
  schedule: z.string().optional(),
  baseEducation: z.string().optional(),
  baseType9: z.string().optional(),
  baseType12: z.string().optional(),
});

export type OpportunityEdit = z.infer<typeof opportunityEditSchema>;
