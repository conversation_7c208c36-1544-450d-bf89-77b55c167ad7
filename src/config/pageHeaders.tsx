import { VIEW } from '@/constants/enums';
import {
  AddCircleRounded,
  CancelRounded,
  CheckCircleRounded,
  PersonRounded,
  RemoveRedEyeRounded,
  UploadRounded,
} from '@mui/icons-material';

export interface PageHeaderConfig {
  title: string;
  subtitle?: string;
  disableHeaderActions?: boolean;
  action?: {
    href?: string;
    view?: VIEW;
    label: string;
    shouldCloseTab?: boolean;
    onClick?: () => void;
    icon?: () => React.ReactNode;
    menu?: Array<{
      label: string;
      icon?: () => React.ReactNode;
      href?: string;
      onClick?: string;
    }>;
  };
  secondaryAction?: {
    href?: string;
    view?: VIEW;
    label: string;
    shouldCloseTab?: boolean;
    icon?: () => React.ReactNode;
    shown?: () => boolean;
    menu?: Array<{
      label: string;
      icon?: () => React.ReactNode;
      href?: string;
      onClick?: () => void;
    }>;
  };
  actions?: React.ReactNode;
}
//TODO: Add translated page headers
export const PAGE_HEADERS = (): [string, PageHeaderConfig][] => [
  [
    '/dashboard/profiles/new',
    {
      title: 'profiles.new.title',
      subtitle: 'profiles.new.subtitle',
    },
  ],
  [
    '/dashboard/profiles/:id',
    {
      title: 'profiles.edit.title',
      subtitle: 'profiles.edit.subtitle',
      // action: {
      //   label: `contacts.users.${view === VIEW.VIEW ? VIEW.EDIT : 'save'}`,
      //   icon: () => (view === VIEW.VIEW ? <EditIcon /> : <SaveIcon />),
      //   shouldCloseTab: true,
      // },
      // secondaryAction: {
      //   shown: () => view !== VIEW.VIEW,
      //   label: `contacts.users.cancel`,
      //   shouldCloseTab: true,
      // },
    },
  ],
  [
    '/dashboard/profiles/',
    {
      title: 'profiles.title',
      subtitle: 'profiles.subtitle',
      action: {
        href: '/dashboard/profiles/new',
        label: 'profiles.label',
        icon: () => <AddCircleRounded />,
      },
      // exemplo de actions customizadas
      // actions: (
      //   <Button
      //     component={Link}
      //     href="/dashboard/profiles/new"
      //     variant="contained"
      //     startIcon={<AddCircleRounded />}
      //   >
      //     Novo Perfil
      //   </Button>
      // ),
    },
  ],
  [
    '/dashboard/contacts/users/:id',
    {
      title: 'contacts.users.title',
      //subtitle: 'profiles.edit.subtitle',
    },
  ],
  [
    '/dashboard/crm/leads',
    {
      title: 'crm.leads.title',
      subtitle: 'crm.leads.subtitle',
      action: {
        href: '/dashboard/crm/leads',
        label: 'crm.leads.label',
        icon: () => <UploadRounded />,
      },
    },
  ],
  [
    '/dashboard/crm/leads/:id',
    {
      title: 'crm.leads.lead.title',
      action: {
        href: '/dashboard/crm/leads/:id/edit',
        label: 'crm.leads.lead.secondaryLabel',
        menu: [
          { label: 'Reatribuir lead', icon: () => <PersonRounded />, onClick: 'reassignLead' },
          {
            label: 'Fechar c/ sucesso',
            icon: () => <CheckCircleRounded color="success" />,
            onClick: 'closeSuccess',
          },
          {
            label: 'Fechar s/ sucesso',
            icon: () => <CancelRounded color="error" />,
            onClick: 'closeFail',
          },
        ],
      },
      secondaryAction: {
        href: '/dashboard/contacts/users/:id',
        label: 'crm.leads.lead.label',
        icon: () => <RemoveRedEyeRounded />,
      },
    },
  ],
  [
    '/dashboard/crm/opportunities',
    {
      title: 'crm.opportunities.title',
      subtitle: 'crm.opportunities.subtitle',
      action: {
        href: '/dashboard/crm/opportunities/new',
        label: 'crm.opportunities.label',
        icon: () => <AddCircleRounded />,
      },
    },
  ],
  [
    '/dashboard/contacts/entities/new/:id',
    {
      title: 'contacts.entities.title',
    },
  ],
  [
    '/dashboard/crm/opportunities/new',
    {
      title: 'crm.opportunities.new.title',
      subtitle: 'crm.opportunities.new.subtitle',
    },
  ],
  [
    '/dashboard/users',
    {
      title: 'users.title',
      subtitle: 'users.subtitle',
      action: {
        href: '/dashboard/users/new',
        label: 'users.label',
        icon: () => <AddCircleRounded />,
      },
    },
  ],
  [
    '/dashboard/users/new/:id',
    {
      title: 'users.label',
    },
  ],
  [
    '/dashboard/users/:id',
    {
      title: 'users.label',
      subtitle: 'users.userForm.editUserSubtitle',
    },
  ],
];
