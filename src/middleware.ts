import { NextRequest, NextResponse } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import { getToken } from 'next-auth/jwt';
import { RouteRule } from './lib/types/ability';
import { buildAbility, can } from './lib/auth/ability';

// 1) internationalization middleware
const intlMiddleware = createIntlMiddleware({
  locales: ['pt', 'en'],
  defaultLocale: 'pt',
});

// 2) map protected routes to required permission codes
const permissionMap: Record<string, RouteRule> = {
  '/dashboard/leads': { op: 'read', mod: 'CRM', func: 'LEADS' },
  '/dashboard/settings': { op: 'update', mod: 'CORE', func: 'SETTINGS' },
  '/dashboard/profiles': { op: 'read', mod: 'CRM', func: 'PROFILES' },
  '/dashboard/profiles/new': { op: 'create', mod: 'CRM', func: 'PROFILES' },
  '/dashboard/profiles/:id': { op: 'update', mod: 'CRM', func: 'PROFILES' },
  // add more mappings as needed...
};

const DATA_HEADERS = ['x-nextjs-data', 'x-middleware-prefetch'];

function to403(req: NextRequest) {
  const url = req.nextUrl.clone();
  url.pathname = `/403`;
  url.search = url.hash = '';
  return url;
}
function securityHeaders(res: NextResponse) {
  const isDev = process.env.NODE_ENV !== 'production';
  const csp = [
    "default-src 'self'",
    `script-src 'self' 'unsafe-inline'${isDev ? " 'unsafe-eval'" : ''} https://trusted.cdn.com`,
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https://images.unsplash.com",
    "connect-src 'self' https://api.yourdomain.com",
  ].join('; ');

  res.headers.set('Content-Security-Policy', csp);
  res.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.headers.set('X-Frame-Options', 'DENY');
}

export async function middleware(req: NextRequest) {
  // run the i18n middleware first to handle locale detection/rewrite
  const res = intlMiddleware(req);

  // determine the current pathname and locale
  const locale = req.nextUrl.locale ?? 'pt'; // ex.: 'pt'
  const rawPath = req.nextUrl.pathname; // ex.: '/pt/dashboard/leads'
  const pathname = rawPath.startsWith(`/${locale}/`) ? rawPath.slice(locale.length + 1) : rawPath;

  //TODO: REMOVE COMMENT PART TO PERMISSIONS WORK
  // if this route requires a permission check…
  // const rule = Object.entries(permissionMap).find(
  //   ([prefix]) => pathname === prefix || pathname.startsWith(prefix + '/')
  // )?.[1] as RouteRule | undefined;

  // if (rule) {
  //   // retrieve the JWT token from NextAuth (with ability)
  //   const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  //   const ability = token?.raw ? buildAbility(token.raw) : undefined;
  //   // if no token or user lacks the necessary role, rewrite to 403 page
  //   if (!ability || !can(ability, rule.op, rule.mod, rule.func, rule.minLvl)) {
  //     const target = to403(req);
  //     const isData = DATA_HEADERS.some((h) => req.headers.get(h) === '1');
  //     const response = isData ? NextResponse.rewrite(target) : NextResponse.redirect(target);

  //     return response;
  //   }
  // }

  // 3) set security headers (CSP, referrer policy, etc.)
  securityHeaders(res);
  return res;
}

// apply this middleware to both the i18n routes and the dashboard routes
export const config = {
  matcher: [
    // run on all pages _except_
    //  - anything under /_next
    //  - API routes under /api
    //  - files that contain a dot (e.g. favicon.ico, robots.txt, images, etc.)
    '/((?!_next/|api/|.*\\..*$|/403$).*)',

    // …plus your protected dashboard routes
    '/dashboard/:path*',
    // dashboard with locale → /pt/dashboard/**  /en/dashboard/**
    '/:locale(pt|en)/dashboard/:path*',
  ],
};
