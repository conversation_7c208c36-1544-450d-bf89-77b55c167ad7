'use client';

import { useMemo } from 'react';
import { ComposableForm } from '../../../../../../../components/form-builder/ComposableFormBuilder';
import { uniq } from 'lodash';
import { useFormContext, useWatch } from 'react-hook-form';
import { useRegionalData } from '../../../../../../../lib/queries/regions';

type AddressInput = {
  distrito?: string;
  concelho?: string;
  freguesia?: string;
};

export function AddressFields() {
  const { regions, loading } = useRegionalData();

  const { control } = useFormContext<AddressInput>();

  const [distrito, concelho] = useWatch({
    control,
    //in the future maybe adapt these field names
    name: ['distrito', 'concelho'],
  });

  const distritos = useMemo(() => uniq(regions.map((g) => g.distrito)), [regions]);

  const concelhos = useMemo(
    () =>
      distrito ? uniq(regions.filter((g) => g.distrito === distrito).map((g) => g.concelho)) : [],
    [regions, distrito]
  );

  const freguesias = useMemo(
    () =>
      concelho && distrito
        ? uniq(regions.filter((g) => g.concelho === concelho).map((g) => g.freguesia))
        : [],
    [regions, concelho, distrito]
  );

  const distritoOptions = useMemo(
    () => distritos.map((d) => ({ value: d, label: d })),
    [distritos]
  );
  const concelhoOptions = useMemo(
    () => concelhos.map((d) => ({ value: d, label: d })),
    [concelhos]
  );
  const freguesiaOptions = useMemo(
    () => freguesias.map((d) => ({ value: d, label: d })),
    [freguesias]
  );

  return (
    <>
      <ComposableForm.Field
        name="distrito"
        label="forms.distrito.label"
        placeholder="forms.distrito.placeholder"
        type="select"
        loading={loading}
        options={distritoOptions}
      />
      <ComposableForm.Field
        name="concelho"
        label="forms.concelho.label"
        placeholder="forms.concelho.placeholder"
        type="select"
        loading={loading}
        disabled={!distrito}
        options={concelhoOptions}
      />
      <ComposableForm.Field
        name="freguesia"
        label="forms.freguesia.label"
        placeholder="forms.freguesia.placeholder"
        type="select"
        disabled={!distrito || !concelho}
        loading={loading}
        options={freguesiaOptions}
      />
    </>
  );
}
