'use client';

import { useForm } from 'react-hook-form';
import { SectionCardInternal, SectionTab } from '../../../../../../../components/ui/SectionCard';
import { useMemo } from 'react';
import { onSubmitEntity } from '../actions';
import { EntityInputSchema } from '../../../../../../../schemas/entitySchema';
import { useTabStore } from '../../../../../../../store/tabStore';
import { useShallow } from 'zustand/react/shallow';
import { GDPRForm } from '../../../../../../../components/form-builder/presets/gdprForm';
import { ComposableForm } from '../../../../../../../components/form-builder/ComposableFormBuilder';
import { useTranslations } from 'next-intl';
import { ObservationsField } from '../../../../../../../components/form-builder/presets/observationsField';
import { NameField } from '../../../../../../../components/form-builder/presets/nameField';
import { EmailField } from '../../../../../../../components/form-builder/presets/emailField';
import { PhoneField } from '../../../../../../../components/form-builder/presets/phoneField';
import { NifField } from '../../../../../../../components/form-builder/presets/nifField';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useDefaultValuesUnsaved,
  useRegisterFormGetValues,
  useTabsDynamicInitializer,
  useTabsMethods,
} from '@/hooks/useTabs';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { VIEW } from '../../../../../../../constants/enums';
import { usePathname } from 'next/navigation';
import { pathWithoutLocale } from '../../../../../../../utils';
import { AddressFields } from './addressFields';

enum EntityFormTabs {
  DETAILS = 'details',
  GDPR = 'gdpr',
}
function getTabColumns(tab: EntityFormTabs): 1 | 2 | 3 | 4 {
  switch (tab) {
    case EntityFormTabs.DETAILS:
      return 2;
    case EntityFormTabs.GDPR:
      return 1;
    // ...other cases
    default:
      return 2;
  }
}
// missing distinction between new and not new to properly reder form tabs
// instead of any, use entitytype
export function EntitiesPageClient({ initial }: { initial?: any }) {
  const t = useTranslations();
  const { updateSubTab } = useTabsMethods();

  // used for ids
  const tabId = pathWithoutLocale(usePathname());
  // used for defautlvalues and unsaved changes
  const defaultValues = useDefaultValuesUnsaved(initial, EntityInputSchema);
  useTabsDynamicInitializer({ initial, location: tabId });

  const methods = useForm({
    resolver: zodResolver(EntityInputSchema),
    mode: 'onSubmit', // TODO be decided
    defaultValues,
  });

  const { getValues, handleSubmit } = methods;
  // used for unsaved changes registration
  useRegisterFormGetValues(tabId, getValues);

  // if you arent using TABS, change these to local states
  // use view for readmode on fields
  const { view, subTab } = useTabStore(
    useShallow((state) => ({
      view: state.openTabs.find((t) => t.isOpen)?.view,
      subTab: state.openTabs.find((t) => t.isOpen)?.subTab ?? EntityFormTabs.DETAILS,
    }))
  );

  const tabs = useMemo<SectionTab[]>(
    () => [
      {
        label: 'entities.details',
        value: EntityFormTabs.DETAILS,
      },
      {
        label: 'gdpr',
        value: EntityFormTabs.GDPR,
      },
    ],
    []
  );
  const onSubmit = async (data: any) => {
    // Only submit if there are no validation errors
    await onSubmitEntity(data);
  };

  const onError = (errors: any) => {
    console.log('Validation errors:', errors);
  };

  return (
    <SectionCardInternal
      tabs={tabs}
      selectedTab={subTab}
      addSpacer
      onSelectTab={(tab) => {
        updateSubTab({ subTab: String(tab) });
      }}
    >
      <ComposableFormWrapper
        columns={getTabColumns(subTab as EntityFormTabs)}
        methods={methods}
        view={view as VIEW}
        onSubmit={handleSubmit(onSubmit, onError)}
      >
        {subTab === EntityFormTabs.DETAILS && (
          <>
            <NameField />
            <EmailField />
            <PhoneField />
            <NifField />
            <ComposableForm.Field
              name="website"
              label="entities.fields.website.label"
              placeholder="entities.fields.website.placeholder"
              type="text"
            />
            <ComposableForm.Field
              name="location"
              label="forms.location.label"
              placeholder="forms.location.placeholder"
              type="text"
            />
            <AddressFields />
            <ComposableForm.Field
              name="capital_social"
              label="entities.fields.capital_social.label"
              placeholder="entities.fields.capital_social.placeholder"
              type="number"
              endAdornment={'€'}
            />
            <ComposableForm.Field
              name="type"
              label="entities.fields.type.label"
              placeholder="entities.fields.type.placeholder"
              multiple
              type="select"
              colSpan={12}
              options={[
                {
                  value: 'cliente_formacao',
                  label: t('entities.fields.cliente_formacao.label'),
                },
                {
                  value: 'cliente_faturacao',
                  label: t('entities.fields.cliente_faturacao.label'),
                },
                {
                  value: 'entidade_empregadora',
                  label: t('entities.fields.entidade_empregadora.label'),
                },
                { value: 'parceria', label: t('entities.fields.parceria.label') },
              ]}
            />
            <ObservationsField />
            <ComposableForm.Field
              name="protocols"
              label="entities.protocols"
              type="multiFieldLine"
              colSpan={12}
            >
              <ComposableForm.Field
                name={'praticas'}
                label={'entities.fields.praticas.label'}
                type={'checkbox'}
                colSpan={6}
              />
              <ComposableForm.Field
                name={'estagios'}
                label={'entities.fields.estagios.label'}
                type={'checkbox'}
                colSpan={6}
              />
            </ComposableForm.Field>
          </>
        )}
        {subTab === EntityFormTabs.GDPR && <GDPRForm />}
      </ComposableFormWrapper>
    </SectionCardInternal>
  );
}
