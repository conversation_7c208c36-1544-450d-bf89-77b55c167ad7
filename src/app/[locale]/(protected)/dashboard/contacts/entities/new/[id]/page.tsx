'use server';

import { EntitiesPageClient } from '../../components/entityFormPage';
import { PageProps } from '../../../../../../../../types';

export default async function Page({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const entityInfo = { id: urlParams.id };

  // new way with updated formbuilder using composition
  return <EntitiesPageClient initial={entityInfo} />;
}
