'use client';

import IconButtonText from '@/components/form-builder/inputs/IconButtonText';
import DataTable from '@/components/ui/DataTable';
import RowActions, { ActionItem } from '@/components/ui/RowActions';
import { AddCircleRounded, Delete, Edit, Visibility } from '@mui/icons-material';
import { VIEW } from '@/constants/enums';
import { contactUserSchema } from '@/schemas/contactUserSchema';
import {
  CONTACT_USER_SUBTAB,
  ContactUserSubTabType,
  ContactUserType,
  CRMHistoryType,
  SelectOptions,
} from '@/types/contactUser';
import { pathWithoutLocale } from '@/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { ColumnDef } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { SectionCardInternal, SectionTab } from '@/components/ui/SectionCard';
import {
  useDefaultValuesUnsaved,
  useRegisterFormGetValues,
  useTabsDynamicInitializer,
  useTabsMethods,
} from '@/hooks/useTabs';
import { useTabStore } from '@/store/tabStore';
import { useShallow } from 'zustand/react/shallow';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { GDPRForm } from '@/components/form-builder/presets/gdprForm';
import {
  AttachmentsFields,
  ContactUserDetailsFields,
  SequenceFields,
} from '@/components/form-builder/formConfigs/userConfig';
import { EditInteractionDialog } from '../../../crm/opportunities/[opportunityId]/_components/editInteraction';

export type FormProps = {
  mode: VIEW;
  initial?: ContactUserType | null;
  selectOptions?: SelectOptions;
};

// TO TEST THIS URL:
// http://localhost:3000/pt/dashboard/contacts/users/58880
// you can access this via leads page now

function getTabColumns(tab: ContactUserSubTabType): 1 | 2 | 3 | 4 {
  switch (tab) {
    case CONTACT_USER_SUBTAB.ATTACHMENTS:
      return 3;
    case CONTACT_USER_SUBTAB.DETAILS:
      return 2;
    case CONTACT_USER_SUBTAB.RGPD:
    case CONTACT_USER_SUBTAB.RELATIONSHIPS:
    case CONTACT_USER_SUBTAB.ENTITIES:
    case CONTACT_USER_SUBTAB.CRM_HISTORY:
    case CONTACT_USER_SUBTAB.ENROLLMENTS:
    case CONTACT_USER_SUBTAB.PAYMENTS:
      return 1;
    default:
      return 2;
  }
}

export default function ContactUser({ mode, initial, selectOptions }: FormProps) {
  const t = useTranslations();
  // used for ids
  const tabId = pathWithoutLocale(usePathname());

  // used for defautlvalues and unsaved changes
  const defaultValues = useDefaultValuesUnsaved(initial, contactUserSchema);

  // if you arent using TABS, change these to local states
  const { view, subTab } = useTabStore(
    useShallow((state) => ({
      view: state.openTabs.find((t) => t.isOpen)?.view,
      subTab: state.openTabs.find((t) => t.isOpen)?.subTab ?? CONTACT_USER_SUBTAB.DETAILS,
    }))
  );
  const readonly = (view ?? mode) === VIEW.VIEW;
  const { updateSubTab, handleChangeTab } = useTabsMethods();

  // used for dynamic tabs initialization
  useTabsDynamicInitializer({ initial, location: tabId });

  const [formOptions, setFormOptions] = useState(() => ({
    relationshipsTypes: Object?.keys?.(defaultValues?.relationships ?? {}),
    files: Object.keys(defaultValues?.attachments ?? {}).map((id) => ({ id, published: true })),
  }));

  const [openEditOpportunityInteractionModal, setOpenEditOpportunityInteractionModal] =
    useState<CRMHistoryType | null>(null);

  const [openViewSystemInteractionModal, setOpenViewSystemInteractionModal] =
    useState<CRMHistoryType | null>(null);

  //3. put unsaved changes if exist on form
  const methods = useForm<any>({
    resolver: zodResolver(contactUserSchema),
    defaultValues,
  });
  const { handleSubmit, getValues } = methods;

  // used for unsaved changes registration
  useRegisterFormGetValues(tabId, getValues);

  const onSubmit = () => {
    //handleSubmit()
    // handle form submission
    console.log('Form submitted with values:', getValues());
  };

  const onReset = () => {
    console.log('Form reset');
    methods.reset({ ...initial });
  };

  const getCrmHistoryActions = (item: CRMHistoryType): ActionItem[] => {
    switch (item.category) {
      case 'INTERACTION_EXTERNAL':
        return [
          {
            label: 'Ver detalhes',
            icon: <Visibility />,
            onClick: () =>
              handleChangeTab({
                id: `/dashboard/crm/${item.type}/${item.id}`,
                title: item.description ?? `Interação #${item.id}`,
              }),
          },
          {
            label: 'Eliminar interação',
            icon: <Delete />,
            color: 'error',
            sx: { '& .MuiListItemText-primary': { color: 'error.main' } },
            onClick: () => console.info('Eliminar interação', item),
          },
        ];
      case 'INTERACTION_OPPORTUNITY':
        return [
          {
            label: 'Editar interação',
            icon: <Edit />,
            onClick: () => {
              setOpenEditOpportunityInteractionModal(item);
            },
          },
        ];
      case 'INTERACTION_SYSTEM':
        return [
          {
            label: 'Ver detalhes',
            icon: <Edit />,
            onClick: () => {
              setOpenViewSystemInteractionModal(item);
            },
          },
        ];
    }
  };

  const baseColumns = useMemo<ColumnDef<CRMHistoryType>[]>(
    () => [
      { accessorKey: 'id', header: 'ID', minSize: 128 },
      {
        accessorKey: 'description',
        header: 'Descrição',
        minSize: 128,
      },
      {
        accessorKey: 'type',
        header: 'Tipo',
        minSize: 128,
      },
      {
        accessorKey: 'madeBy',
        header: 'Efetuado por',
        minSize: 128,
      },
      {
        accessorKey: 'creationDate',
        header: 'Criado por',
        minSize: 128,
      },
      {
        accessorKey: 'enrollment',
        header: 'Matrícula',
        minSize: 128,
      },
    ],
    []
  );

  const columns = useMemo<ColumnDef<CRMHistoryType>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => <RowActions actions={getCrmHistoryActions(row.original)} />,
      },
    ],
    [baseColumns]
  );

  const detailsFields = useMemo(
    () => <ContactUserDetailsFields readonly={readonly} selectOptions={selectOptions} />,
    [readonly, selectOptions]
  );

  const rgpdFields = useMemo(() => <GDPRForm readonly={readonly} />, [readonly]);

  const relationshipsFields = useMemo(
    () => (
      <SequenceFields
        formOptions={formOptions}
        selectOptions={selectOptions}
        readonly={readonly}
        setFormOptions={setFormOptions}
        fieldKey="relationships"
      />
    ),
    [formOptions, selectOptions, readonly, setFormOptions]
  );

  const entitiesFields = useMemo(
    () => (
      <SequenceFields
        formOptions={formOptions}
        selectOptions={selectOptions}
        readonly={readonly}
        setFormOptions={setFormOptions}
        fieldKey="entities"
      />
    ),
    [formOptions, selectOptions, readonly, setFormOptions]
  );

  const attachmentsFields = useMemo(
    () => (
      <AttachmentsFields
        readonly={readonly}
        formOptions={formOptions}
        setFormOptions={setFormOptions}
        selectOptions={selectOptions}
        methods={methods}
      />
    ),
    [readonly, formOptions, setFormOptions, selectOptions, methods]
  );

  const crmHistoryFields = useMemo(
    () => (
      <>
        <DataTable<CRMHistoryType>
          data={initial?.crmHistory ?? []}
          columns={columns}
          enableFilters={false}
          removePaginationOnLowResults
        />
        <IconButtonText
          field={{
            readonly: readonly,
            icon: AddCircleRounded,
            action: () => /*setOpen(true)**/ console.log('shoot popuip'),
            actionLabel: 'forms.crmHistory.addInteraction',
          }}
          t={t}
        />
      </>
    ),
    [initial, columns, readonly, t]
  );

  function renderActiveSubTab(activeSubTab: ContactUserSubTabType) {
    switch (activeSubTab) {
      case CONTACT_USER_SUBTAB.DETAILS:
        return detailsFields;
      case CONTACT_USER_SUBTAB.RGPD:
        return rgpdFields;
      case CONTACT_USER_SUBTAB.RELATIONSHIPS:
        return relationshipsFields;
      // DONT SWITCH TO ENTITIES, IT BREAKS
      case CONTACT_USER_SUBTAB.ENTITIES:
        return entitiesFields;
      case CONTACT_USER_SUBTAB.ATTACHMENTS:
        return attachmentsFields;
      case CONTACT_USER_SUBTAB.CRM_HISTORY:
        return crmHistoryFields;
      case CONTACT_USER_SUBTAB.ENROLLMENTS:
        break;
      case CONTACT_USER_SUBTAB.PAYMENTS:
        break;
      default:
        return null;
    }
  }

  const tabs = useMemo<SectionTab[]>(() => {
    const allTabs = Object.values(CONTACT_USER_SUBTAB).map((value) => ({
      label: `forms.subtab.${value}`,
      value,
    }));
    if ((view ?? mode) === VIEW.CREATE) {
      return allTabs.filter(
        (tab) => tab.value === CONTACT_USER_SUBTAB.DETAILS || tab.value === CONTACT_USER_SUBTAB.RGPD
      );
    }
    return allTabs;
  }, [t, view, mode]);

  return (
    <>
      <SectionCardInternal
        tabs={tabs}
        addSpacer
        selectedTab={subTab ?? CONTACT_USER_SUBTAB.DETAILS}
        onSelectTab={(subTab) => updateSubTab({ subTab: String(subTab) })}
      >
        <ComposableFormWrapper
          columns={getTabColumns(subTab ?? CONTACT_USER_SUBTAB.DETAILS)}
          methods={methods}
          onSubmit={handleSubmit(onSubmit)}
          onReset={onReset}
          view={view ?? mode}
        >
          {renderActiveSubTab(subTab ?? CONTACT_USER_SUBTAB.DETAILS)}
        </ComposableFormWrapper>
      </SectionCardInternal>
      {openEditOpportunityInteractionModal ? (
        <EditInteractionDialog
          onClose={() => setOpenEditOpportunityInteractionModal(null)}
          opportunityId={openEditOpportunityInteractionModal.extraData}
          interactionId={openEditOpportunityInteractionModal.id}
        />
      ) : null}

      {openViewSystemInteractionModal ? (
        <EditInteractionDialog
          onClose={() => setOpenViewSystemInteractionModal(null)}
          opportunityId={openViewSystemInteractionModal.extraData}
          interactionId={openViewSystemInteractionModal.id}
        />
      ) : null}
    </>
  );
}
