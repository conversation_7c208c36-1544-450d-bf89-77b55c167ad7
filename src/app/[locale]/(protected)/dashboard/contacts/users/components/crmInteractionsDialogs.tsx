import { useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { ContactUserType } from "../../../../../../../types/contactUser";
import { ComposableForm } from "../../../../../../../components/form-builder/ComposableFormBuilder";
import FormBuilderDialog from "../../../../../../../components/ui/dialog/FormBuilderDialog";
import { useForm } from "react-hook-form";
import { OpportunityInteractionFormFields } from "../../../crm/opportunities/[opportunityId]/_components/editInteraction";
import { DescriptionField } from "../../../../../../../components/form-builder/presets/descriptionField";
import { interactionMocks } from "../../../../../../../lib/mocks/interactions";

type SystemInteraction = {
 description: string;
 doneBy: ContactUserType;
 createdAt: Date;
 text: string
}

export function PreviewSystemInteraction({interactionId,onClose}: {interactionId: string,onClose: () => any}){

  const t = useTranslations();

  const client = useQueryClient();

  const { data,isLoading: loading } = useSuspenseQuery<SystemInteraction>({
    queryKey: ['interactions', {system: true},interactionId],
    queryFn: () => interactionMocks[0],
  });


  const methods = useForm<SystemInteraction>({
    defaultValues: data,
  });

  return (
    <ComposableForm.Provider methods={methods} columns={1}>
      <FormBuilderDialog
        open={true}
        onCancel={() => {
          onClose();
        }}
        title={t('dashboard.crm.opportunities.interactions.editDialog', {
          id: interactionId,
        })}
      >
        <OpportunityInteractionFormFields />
      <DescriptionField loading={loading} />
      <ComposableForm.Field
        type="datetime"
        label={tPrefix('fields.doneDate.label')}
        name={'doneDate'}
        loading={loading}
      />
      </FormBuilderDialog>
    </ComposableForm.Provider>
}