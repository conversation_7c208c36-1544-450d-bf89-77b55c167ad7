import { fetchContactUserOptions, fetchContactUsers, fetchFileTypes } from '@/lib/api/contactUsers';
import { notFound } from 'next/navigation';
import ContactUser from '../components/ContactUser';
import { VIEW } from '@/constants/enums';
import { fetchEmployerEntities } from '@/lib/api/entities';
import { PageProps } from '../../../../../../../types';

export default async function ContactUserPage({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const users = await fetchContactUsers();
  const contactUser = users.find((p) => p.id === urlParams.id);
  const options = await fetchContactUserOptions();
  options.users = users.map((user) => ({ value: user.id, label: user.name || '' }));
  options.fileTypes = await fetchFileTypes();
  options.employer = await fetchEmployerEntities();
  // if id starts with new, it should continue because its a new contact user
  if (!contactUser) return notFound();

  return <ContactUser mode={VIEW.VIEW} initial={contactUser} selectOptions={options} />;
}
