import { fetchContactUserOptions, fetchContactUsers, fetchFileTypes } from '@/lib/api/contactUsers';
import ContactUser from '../../components/ContactUser';
import { VIEW } from '@/constants/enums';
import { fetchEmployerEntities } from '@/lib/api/entities';
import { PageProps } from '../../../../../../../../types';

export default async function ContactUserPage({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const users = await fetchContactUsers();
  const options = await fetchContactUserOptions();
  options.users = users.map((user) => ({ value: user.id, label: user.name || '' }));
  options.fileTypes = await fetchFileTypes();
  options.employer = await fetchEmployerEntities();

  const contactUser = {
    id: urlParams.id,
  };

  return <ContactUser mode={VIEW.CREATE} initial={contactUser} selectOptions={options} />;
}
