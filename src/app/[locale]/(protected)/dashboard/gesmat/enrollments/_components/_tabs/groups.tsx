import { useMemo } from 'react';
import DataTable from '../../../../../../../../components/ui/DataTable';
import { useTranslations } from 'next-intl';
import { Button } from '@mui/material';
import { AddCircle, Delete, SwapVerticalCircle, VisibilityTwoTone } from '@mui/icons-material';
import RowActions from '../../../../../../../../components/ui/RowActions';

const mockGroups = [
  {
    ref: 'GRP-001',
    status: 'Active',
    course: 'Course A',
    center: 'Polo Norte',
    modality: 'Presential',
    schedule: '08:00-12:00',
    expectedStart: '2024-07-01',
    daysSinceOldestEnrollment: 10,
    numTrainees: 25,
    fundraisingDeadline: '2024-06-25',
    startDate: '2024-07-01',
    assignedTo: '<PERSON>',
    createdBy: '<PERSON>',
    createdAt: '2024-06-01',
  },
  {
    ref: 'GRP-002',
    status: 'Inactive',
    course: 'Course B',
    center: 'Polo Sul',
    modality: 'Online',
    schedule: '14:00-18:00',
    expectedStart: '2024-08-10',
    daysSinceOldestEnrollment: 5,
    numTrainees: 18,
    fundraisingDeadline: '2024-08-01',
    startDate: '2024-08-10',
    assignedTo: 'Ana Lima',
    createdBy: 'Carlos Dias',
    createdAt: '2024-07-10',
  },
];

export function GesmatEnrollmentGroupTab() {
  const t = useTranslations('dashboard.gesmat.enrollments.groups');

  const columns = useMemo(
    () => [
      { accessorKey: 'ref', header: t('columns.ref') },
      { accessorKey: 'status', header: t('columns.status') },
      { accessorKey: 'course', header: t('columns.course') },
      { accessorKey: 'center', header: t('columns.center') },
      {
        accessorKey: 'modality',
        header: t('columns.modality'),
      },
      {
        accessorKey: 'schedule',
        header: t('columns.schedule'),
      },
      {
        accessorKey: 'expectedStart',
        header: t('columns.expectedStart'),
      },
      {
        accessorKey: 'daysSinceOldestEnrollment',
        header: t('columns.daysSinceOldestEnrollment'),
      },
      {
        accessorKey: 'numTrainees',
        header: t('columns.numTrainees'),
      },
      {
        accessorKey: 'fundraisingDeadline',
        header: t('columns.fundraisingDeadline'),
      },
      {
        accessorKey: 'startDate',
        header: t('columns.startDate'),
      },
      {
        accessorKey: 'assignedTo',
        header: t('columns.assignedTo'),
      },
      {
        accessorKey: 'createdBy',
        header: t('columns.createdBy'),
      },
      {
        accessorKey: 'createdAt',
        header: t('columns.createdAt'),
      },
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: () => (
          <RowActions
            actions={[
              {
                icon: <VisibilityTwoTone />,
                label: t('list'),
                onClick: () => null,
              },
              {
                icon: <SwapVerticalCircle />,
                label: t('transfer'),
                onClick: () => null,
              },
              {
                icon: <Delete />,
                sx: {
                  '*': {
                    color: 'red !important',
                  },
                },
                label: t('delete'),
                onClick: () => null,
              },
            ]}
          />
        ),
      },
    ],
    [t]
  );

  const handleRowClick = () => {};

  return (
    <>
      <DataTable
        data={mockGroups}
        columns={columns}
        enableFilters={false}
        onRowClick={handleRowClick}
      />
      <Button sx={{ alignSelf: 'flex-start', padding: 0 }} startIcon={<AddCircle />}>
        {t('add')}
      </Button>
    </>
  );
}
