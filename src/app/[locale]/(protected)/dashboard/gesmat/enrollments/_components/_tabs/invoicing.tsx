import { useFormContext, useWatch } from 'react-hook-form';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { ProfileSelectField } from '../../../../../../../../components/form-builder/presets/profileSelectField';
import { TraineeTypeField } from '../../../../../../../../components/form-builder/presets/traineeTypeField';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import SectionCard from '../../../../../../../../components/ui/SectionCard';
import { useOptionsIntl } from '../../../../../../../../hooks/useFieldOptionsIntl';
import { InsuranceFields } from '../insuranceFields';
import { useEffect } from 'react';

const tPrefix = (x: string) => `dashboard.gesmat.enrollments.${x}`;

const enrollmentOptions: FieldOption[] = [
  {
    label: '75€',
    value: 75,
  },
  {
    label: '120€',
    value: 120,
  },
  {
    label: '145€',
    value: 145,
  },
  {
    label: '150€',
    value: 150,
  },
];

const useRecurringPaymentTypeOptions = () =>
  useOptionsIntl(
    ['reference', 'transfer', 'presential'],
    tPrefix('fields.recurringPaymentTypes.options')
  );

const usePaymentTypeOptions = () =>
  useOptionsIntl(
    ['reference', 'transfer', 'cash', 'card', 'check'],
    tPrefix('fields.paymentTypes.options')
  );

export function GesmantEnrollmentInvoicingTab({ disabled }: { disabled?: boolean }) {
  const recurringPaymentTypeOptions = useRecurringPaymentTypeOptions();
  const paymentTypeOptions = usePaymentTypeOptions();

  const { control, resetField } = useFormContext();

  const [differentInvoiceContact, paymentType] = useWatch({
    control,
    name: ['differentInvoiceContact', 'upfront.paymentType'],
  });

  useEffect(() => {
    if (paymentType !== 'check') {
      resetField('upfront.checkNumber');
      resetField('upfront.bank');
      resetField('upfront.agency');
    }
  }, [paymentType]);

  return (
    <>
      <SectionCard title={tPrefix('contract')}>
        <ComposableForm.Internal.Layout columns={2}>
          <ComposableForm.Field
            label={tPrefix('fields.courseValue.label')}
            placeholder={tPrefix('fields.courseValue.placeholder')}
            name={'courseValue'}
            endAdornment={'€'}
            type={'number'}
            disabled={disabled}
          />
          <ComposableForm.Field
            label={tPrefix('fields.monthlyCount.label')}
            placeholder={tPrefix('fields.monthlyCount.placeholder')}
            name={'monthlyCount'}
            type={'number'}
            disabled={disabled}
          />
          <ComposableForm.Field
            label={tPrefix('fields.enrollmentValue.label')}
            name={'enrollmentValue'}
            options={enrollmentOptions}
            type={'select'}
            disabled={disabled}
          />
          <InsuranceFields disabled={disabled} />
          <ComposableForm.Field
            label={tPrefix('fields.recurringPaymentTypes.label')}
            name={'recurringPaymentType'}
            options={recurringPaymentTypeOptions}
            type={'select'}
            disabled={disabled}
          />
          <ComposableForm.Field
            type={'checkbox'}
            name={'differentInvoiceContact'}
            label={tPrefix('fields.differentInvoiceContact.label')}
            colSpan={12}
            disabled={disabled}
          />
          {differentInvoiceContact === true && (
            <>
              <TraineeTypeField name={'invoiceContactType'} disabled={disabled} />
              <ProfileSelectField name={'invoiceContact'} disabled={disabled} />
            </>
          )}
        </ComposableForm.Internal.Layout>
      </SectionCard>
      <SectionCard title={tPrefix('paymentEnrollment')}>
        <ComposableForm.Internal.Layout columns={2}>
          <ComposableForm.Field
            name={'upfront.monthlyPaymentValue'}
            label={tPrefix('fields.monthlyPaymentValue.label')}
            type={'number'}
            disabled={disabled}
            endAdornment={'€'}
          />
          <ComposableForm.Field
            name={'upfront.totalPaid'}
            label={tPrefix('fields.totalPaid.label')}
            type={'number'}
            endAdornment={'€'}
            disabled={disabled}
          />
          <ComposableForm.Field
            name={'upfront.paymentType'}
            label={tPrefix('fields.paymentType.label')}
            options={paymentTypeOptions}
            type={'select'}
            disabled={disabled}
          />
          {paymentType === 'check' && (
            <>
              <ComposableForm.Field
                name={'upfront.checkNumber'}
                label={tPrefix('fields.checkNumber.label')}
                placeholder={tPrefix('fields.checkNumber.placeholder')}
                type={'number'}
                disabled={disabled}
              />
              <ComposableForm.Field
                name={'upfront.bank'}
                label={tPrefix('fields.bank.label')}
                placeholder={tPrefix('fields.bank.placeholder')}
                type={'text'}
                disabled={disabled}
              />
              <ComposableForm.Field
                name={'upfront.agency'}
                label={tPrefix('fields.agency.label')}
                placeholder={tPrefix('fields.agency.placeholder')}
                type={'text'}
                disabled={disabled}
              />
            </>
          )}
        </ComposableForm.Internal.Layout>
      </SectionCard>
    </>
  );
}
