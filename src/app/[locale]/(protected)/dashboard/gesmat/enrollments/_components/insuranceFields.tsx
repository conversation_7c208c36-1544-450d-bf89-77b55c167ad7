import { useFormContext, useWatch } from 'react-hook-form';
import { ComposableForm } from '../../../../../../../components/form-builder/ComposableFormBuilder';
import { useOptionsIntl } from '../../../../../../../hooks/useFieldOptionsIntl';
import { FieldOption } from '../../../../../../../components/form-builder/types/formBuilder';

const useHasInsuranceOptions = () => useOptionsIntl(['yes', 'no']);

const insuranceValueOptions: FieldOption[] = [
  {
    value: 75,
    label: '75€',
  },
  {
    value: 50,
    label: '50€',
  },
];

export function InsuranceFields({ disabled = false }: { disabled?: boolean }) {
  const hasInsuranceOptions = useHasInsuranceOptions();

  const { control } = useFormContext();

  const [hasInsurance] = useWatch({
    control,
    name: ['hasInsurance'],
  });

  return (
    <>
      <ComposableForm.Field
        name={'hasInsurance'}
        label={'dashboard.gesmat.enrollments.fields.hasInsurance.label'}
        type={'select'}
        options={hasInsuranceOptions}
        disabled={disabled}
      />
      <ComposableForm.Field
        name={'insuranceValue'}
        label={'dashboard.gesmat.enrollments.fields.insuranceValue.label'}
        placeholder={'dashboard.gesmat.enrollments.fields.insuranceValue.placeholder'}
        type={'number'}
        options={insuranceValueOptions}
        disabled={disabled || !hasInsurance || hasInsurance === 'no'}
      />
    </>
  );
}
