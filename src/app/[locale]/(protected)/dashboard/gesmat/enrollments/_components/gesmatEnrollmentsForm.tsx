import { useMemo, useState } from 'react';
import { SectionCardInternal, SectionTab } from '../../../../../../../components/ui/SectionCard';
import { VIEW } from '../../../../../../../constants/enums';
import { FormProvider, useForm } from 'react-hook-form';
import { GesmatEnrollmentDetailsTab } from './_tabs/details';
import { GesmantEnrollmentInvoicingTab } from './_tabs/invoicing';
import { GesmatEnrollmentGroupTab } from './_tabs/groups';
import FormActions from '../../../../../../../components/ui/FormActions';

enum EnrollmentTabs {
  DETAILS = 'details',
  INVOICING = 'invoicing',
  GROUPS = 'groups',
}

export function GesmatEnrollmentsForm({ view }: { view: VIEW }) {
  const [selectedTab, setSelectedTab] = useState<EnrollmentTabs>(EnrollmentTabs.DETAILS);

  const methods = useForm<any>();

  const tabs = useMemo<SectionTab[]>(
    () => [
      {
        label: 'forms.subtab.details',
        value: EnrollmentTabs.DETAILS,
      },
      {
        label: 'forms.subtab.invoicing',
        value: EnrollmentTabs.INVOICING,
      },
      {
        label: 'forms.subtab.groups',
        value: EnrollmentTabs.GROUPS,
      },
    ],
    []
  );

  const disabled = view === VIEW.VIEW;

  const onSubmit = methods.handleSubmit((data) => {
    void data;
  });

  return (
    <form onSubmit={onSubmit}>
      <FormProvider {...methods}>
        <SectionCardInternal tabs={tabs} selectedTab={selectedTab} onSelectTab={setSelectedTab}>
          {selectedTab === EnrollmentTabs.DETAILS && (
            <GesmatEnrollmentDetailsTab disabled={disabled} />
          )}
          {selectedTab === EnrollmentTabs.INVOICING && (
            <GesmantEnrollmentInvoicingTab disabled={disabled} />
          )}
          {selectedTab === EnrollmentTabs.GROUPS && <GesmatEnrollmentGroupTab />}
        </SectionCardInternal>
        <FormActions
          view={view}
          secondActionLabel=""
          secondaryActionLabel=""
          actionLabel="forms.edit"
        />
      </FormProvider>
    </form>
  );
}
