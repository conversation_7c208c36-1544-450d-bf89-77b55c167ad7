import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { BaseLearningFields } from '../../../../../../../../components/form-builder/presets/baseLearningFields';
import { CentersSelectField } from '../../../../../../../../components/form-builder/presets/centersField';
import { EntitySelectField } from '../../../../../../../../components/form-builder/presets/entityField';
import { ObservationsField } from '../../../../../../../../components/form-builder/presets/observationsField';
import { ProfileSelectField } from '../../../../../../../../components/form-builder/presets/profileSelectField';
import { ShiftField } from '../../../../../../../../components/form-builder/presets/shiftField';
import { TraineeTypeField } from '../../../../../../../../components/form-builder/presets/traineeTypeField';
import { useOptionsIntl } from '../../../../../../../../hooks/useFieldOptionsIntl';

const tPrefix = (x: string) => `dashboard.gesmat.enrollments.${x}`;

const useModalityOptions = () =>
  useOptionsIntl(['inPerson', 'bLearning', 'eLearning'], tPrefix('fields.modality.options'));

export function GesmatEnrollmentDetailsTab({ disabled }: { disabled?: boolean }) {
  const modalityOptions = useModalityOptions();

  return (
    <ComposableForm.Internal.Layout columns={2}>
      <ComposableForm.Field
        name={'course'}
        label={tPrefix('fields.course.label')}
        placeholder={tPrefix('fields.course.placeholder')}
        type={'text'}
        disabled={disabled}
      />
      <ComposableForm.Field
        name={'modality'}
        label={tPrefix('fields.modality.label')}
        type={'select'}
        options={modalityOptions}
        disabled={disabled}
      />
      <CentersSelectField name={'center'} disabled={disabled} />
      <ShiftField name={'shift'} disabled={disabled} />
      <BaseLearningFields disabled={disabled} />
      <TraineeTypeField name={'traineeType'} disabled={disabled} />
      <ProfileSelectField name={'student'} disabled={disabled} />
      <EntitySelectField name={'entity'} disabled={disabled} />
      <ObservationsField disabled={disabled} />
    </ComposableForm.Internal.Layout>
  );
}
