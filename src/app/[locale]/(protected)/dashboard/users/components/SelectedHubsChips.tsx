import React, { useRef } from 'react';
import { Chip, Stack } from '@mui/material';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { SelectableItem } from '@/components/form-builder/inputs/SearchableCheckboxList';

interface SelectedHubsChipsProps {
  items: SelectableItem[];
  name?: string;
  isOpen: boolean;
}

export function SelectedHubsChips({ items, name = 'hubs', isOpen }: SelectedHubsChipsProps) {
  const { control, setValue } = useFormContext();
  const watchedHubs: string[] = useWatch({ control, name }) || [];
  const t = useTranslations('dashboard.users');

  // Holds the last confirmed value of hubs
  const lastConfirmedHubsRef = useRef<string[]>(watchedHubs);

  if (!isOpen) {
    lastConfirmedHubsRef.current = watchedHubs;
  }

  const displayedHubs = lastConfirmedHubsRef.current;
  const selectedItems = items.filter((item) => displayedHubs.includes(item.id));

  const handleDelete = (idToDelete: string) => {
    const newHubs = displayedHubs.filter((id) => id !== idToDelete);
    setValue(name, newHubs, { shouldValidate: true, shouldDirty: true });
    lastConfirmedHubsRef.current = newHubs;
  };

  if (!displayedHubs.length) {
    return <>{t('userForm.assign.notAssigned')}</>;
  }

  return (
    <Stack direction="row" spacing={1} flexWrap="wrap">
      {selectedItems.map((item) => (
        <Chip
          key={item.id}
          label={item.label}
          color="primary"
          onDelete={() => handleDelete(item.id)}
        />
      ))}
    </Stack>
  );
}
