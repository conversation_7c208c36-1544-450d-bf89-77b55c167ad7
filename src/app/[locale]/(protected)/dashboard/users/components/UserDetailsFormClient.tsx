'use client';

import React, { useState } from 'react';
import { Box, Typography, TextField, Button, Stack, Chip } from '@mui/material';
import SectionCard from '@/components/ui/SectionCard';
import { usePathname } from 'next/navigation';
import { pathWithoutLocale } from '@/utils';
import { useTabsDynamicInitializer } from '@/hooks/useTabs';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { ComposableForm } from '@/components/form-builder/ComposableFormBuilder';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { useTabStore } from '@/store/tabStore';
import { useShallow } from 'zustand/react/shallow';
import { AddCircleRounded } from '@mui/icons-material';
import FormBuilderDialog from '@/components/ui/dialog/FormBuilderDialog';
import AddHomeIcon from '@mui/icons-material/AddHome';
import { SelectedHubsChips } from './SelectedHubsChips';
import { User } from '@/lib/mocks/users';
import TitleDividerCard from '@/components/ui/TitleDividerCard';

export interface FieldProps {
  label: string;
  value: React.ReactNode;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function Field({ label, value, onChange }: FieldProps) {
  return (
    <Box display="flex" flexDirection="column" gap={0.5}>
      <Typography variant="body2" color="text.secondary">
        {label}
      </Typography>
      <TextField variant="outlined" size="small" value={value} onChange={onChange} fullWidth />
    </Box>
  );
}

export default function UserDetailsFormClient({ user }: { user: User }) {
  const [isOpen, setIsOpen] = useState(false);
  const [initialHubs, setInitialHubs] = useState<string[]>([]);

  // Get pathname without locale, for tabs initializer
  const pathName = pathWithoutLocale(usePathname());

  // Initialize tabs (if you use dynamic tabs for this page)
  useTabsDynamicInitializer({ initial: user, location: pathName });

  // Translation hook
  const t = useTranslations('dashboard.users');

  const { view } = useTabStore(
    useShallow((state) => ({
      view: state.openTabs.find((tab) => tab.isOpen)?.view,
    }))
  );

  const methods = useForm({
    defaultValues: user,
  });

  function handleOpenDialog() {
    const currentHubs = methods.getValues('hubs') || [];
    setInitialHubs(currentHubs);
    setIsOpen(true);
  }

  function handleConfirm() {
    const currentHubs = methods.getValues('hubs') || [];
    setInitialHubs(currentHubs);
    setIsOpen(false);
  }

  function handleCancel() {
    methods.setValue('hubs', initialHubs, {
      shouldValidate: true,
      shouldDirty: true,
    });
    setIsOpen(false);
  }
  function onReset() {}
  const onSubmit = methods.handleSubmit((data) => {
    console.log(data.hubs);
  });

  const items = [
    { label: 'Option 1', id: 'opt1' },
    { label: 'Option 2', id: 'opt2' },
    { label: 'Option 3', id: 'opt3' },
    { label: 'Option 4', id: 'opt4' },
    { label: 'Option 5', id: 'opt5' },
    { label: 'Option 6', id: 'opt6' },
    { label: 'Option 7', id: 'opt7' },
    { label: 'Option 8', id: 'opt8' },
    { label: 'Option 9', id: 'opt9' },
  ];

  return (
    <>
      <TitleDividerCard key={t('userForm.formLabel')} title={t('userForm.formLabel')}>
        <ComposableFormWrapper
          methods={methods}
          onReset={onReset}
          onSubmit={onSubmit}
          view={view!}
          columns={2}
        >
          <ComposableForm.Field
            name="name"
            label="dashboard.users.fields.name.label"
            placeholder="dashboard.users.fields.name.placeholder"
            type="text"
          />
          <ComposableForm.Field
            name="username"
            label="dashboard.users.fields.username.label"
            placeholder="dashboard.users.fields.username.placeholder"
            type="text"
          />
          <ComposableForm.Field
            name="email"
            label="dashboard.users.fields.email.label"
            placeholder="dashboard.users.fields.email.placeholder"
            type="text"
          />
          <ComposableForm.Field
            name="profile"
            label="dashboard.users.fields.profile.label"
            placeholder="dashboard.users.fields.profile.placeholder"
            type="select"
            options={[
              { label: t('fields.profile.admin'), value: 'admin' },
              { label: t('fields.profile.supervisor'), value: 'supervisor' },
            ]}
          />
          <ComposableForm.Field
            name="language"
            label="dashboard.users.fields.language.label"
            placeholder="dashboard.users.fields.language.placeholder"
            type="select"
            options={[
              { label: t('fields.language.pt'), value: 'pt' },
              { label: t('fields.language.en'), value: 'en' },
            ]}
          />

          <ComposableForm.Field
            name="status"
            label="dashboard.users.fields.status.label"
            type="togglebutton"
            options={[
              { label: 'dashboard.users.fields.status.active', value: true },
              { label: 'dashboard.users.fields.status.inactive', value: false },
            ]}
          />
        </ComposableFormWrapper>
      </TitleDividerCard>
      <TitleDividerCard key={t('userForm.assign.section')} title={t('userForm.assign.section')}>
        <ComposableFormWrapper methods={methods} onReset={onReset} onSubmit={onSubmit} view={view!}>
          <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
            <SelectedHubsChips items={items} name="hubs" isOpen={isOpen} />

            <Button
              variant="text"
              color="primary"
              startIcon={<AddCircleRounded />}
              onClick={handleOpenDialog}
            >
              {t('userForm.assign.assign')}
            </Button>
          </Box>
          <FormBuilderDialog
            open={isOpen}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
            title={'dashboard.users.assignDialog.title'}
            confirmLabel={'dashboard.users.assignDialog.confirmLabel'}
            confirmIcon={<AddHomeIcon />}
            maxWidth={'sm'}
          >
            <ComposableForm.Field
              name="hubs"
              label="dashboard.users.assignDialog.instructionText"
              type="SearchableCheckboxList"
              items={items}
            />
          </FormBuilderDialog>
        </ComposableFormWrapper>
      </TitleDividerCard>
    </>
  );
}
