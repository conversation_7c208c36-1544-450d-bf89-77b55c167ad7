'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { ComposableForm } from '@/components/form-builder/ComposableFormBuilder';
import FormBuilderDialog from '@/components/ui/dialog/FormBuilderDialog';
import AddHomeIcon from '@mui/icons-material/AddHome';
import { User } from '@/lib/mocks/users';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { useForm } from 'react-hook-form';
import { VIEW } from '@/constants/enums';

export interface HubsModalProps {
  user: User;
  handleConfirm: () => void;
  handleCancel: () => void;
  isOpen: boolean;
}

export default function HubsModal({ user, handleConfirm, handleCancel, isOpen }: HubsModalProps) {
  const t = useTranslations();

  const methods = useForm({
    defaultValues: user,
  });

  const items = [
    { label: 'Option 1', id: 'opt1' },
    { label: 'Option 2', id: 'opt2' },
    { label: 'Option 3', id: 'opt3' },
    { label: 'Option 4', id: 'opt4' },
    { label: 'Option 5', id: 'opt5' },
    { label: 'Option 6', id: 'opt6' },
    { label: 'Option 7', id: 'opt7' },
    { label: 'Option 8', id: 'opt8' },
    { label: 'Option 9', id: 'opt9' },
  ];

  function onSubmit() {}
  function onReset() {}

  return (
    <ComposableFormWrapper
      methods={methods}
      onReset={onReset}
      onSubmit={onSubmit}
      view={VIEW.CREATE}
      footer={false}
    >
      <FormBuilderDialog
        open={isOpen}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        title={'dashboard.users.assignDialog.title'}
        confirmLabel={'dashboard.users.assignDialog.confirmLabel'}
        confirmIcon={<AddHomeIcon />}
        maxWidth="sm"
      >
        <ComposableForm.Field
          name="hubs"
          label={'dashboard.users.assignDialog.instructionText'}
          type="SearchableCheckboxList"
          items={items}
        />
      </FormBuilderDialog>
    </ComposableFormWrapper>
  );
}
