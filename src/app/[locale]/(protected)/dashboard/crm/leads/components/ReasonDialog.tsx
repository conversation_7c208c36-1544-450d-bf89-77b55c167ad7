'use client';

import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, But<PERSON>, Stack } from '@mui/material';
import { FC } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { ComposableForm } from '@/components/form-builder/ComposableFormBuilder';
import { useTranslations } from 'next-intl';

export interface Option {
  value: string;
  label: string;
}

export interface ReasonDialogProps {
  open: boolean;
  options: Option[];
  leadId: string;
  onClose: () => void;
  onSubmit: (reason: string, description: string) => void;
  confirmIcon?: React.ReactNode;
  confirmLabel?: string;
}

const schema = z.object({
  reason: z.string().min(1, 'forms.required'),
  description: z.string().min(1, 'forms.required'),
});
type ReasonForm = z.infer<typeof schema>;

const ReasonDialog: FC<ReasonDialogProps> = ({
  open,
  leadId,
  options,
  onClose,
  onSubmit,
  confirmIcon,
  confirmLabel,
}) => {
  const t = useTranslations('dashboard.crm.leads.lead.details');

  const methods = useForm<ReasonForm>({
    defaultValues: { reason: '', description: '' },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  const onValid: SubmitHandler<ReasonForm> = ({ reason, description }) => {
    onSubmit(reason, description);
    methods.reset();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('forms.title', { id: leadId })}</DialogTitle>

      <ComposableForm.Provider methods={methods}>
        <DialogContent dividers sx={{ px: 2, py: 3 }}>
          <Stack spacing={2}>
            <ComposableForm.Field
              type="select"
              name="reason"
              required
              label={'dashboard.crm.leads.lead.details.forms.inputLabel'}
              placeholder="dashboard.crm.leads.lead.details.forms.selectReason"
              options={options.map(({ label, value }) => ({ label, value }))}
              fullWidth
            />

            <ComposableForm.Field
              type="text"
              name="description"
              multiline
              required
              label=""
              placeholder="dashboard.crm.leads.lead.details.forms.textFieldPlaceholder"
              fullWidth
            />
          </Stack>
        </DialogContent>
      </ComposableForm.Provider>
      <DialogActions sx={{ py: 2 }}>
        <Button onClick={onClose}>{t('forms.buttons.cancel')}</Button>
        <Button
          type="submit"
          variant="contained"
          disabled={!methods.formState.isValid}
          onClick={methods.handleSubmit(onValid)}
          startIcon={confirmIcon ? confirmIcon : undefined}
        >
          {confirmLabel ? confirmLabel : t('forms.buttons.confirm')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReasonDialog;
