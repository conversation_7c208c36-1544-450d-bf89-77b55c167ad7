'use client';

import { useState, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  <PERSON><PERSON>,
  Stack,
  Alert,
  IconButton,
  Box,
  CircularProgress,
} from '@mui/material';
import { CheckCircleRounded, Visibility } from '@mui/icons-material';
import TableGeneric, { ColumnDef } from '@/components/ui/TableGeneric';
import { useTabsMethods } from '@/hooks/useTabs';
import { Lead } from '@/lib/mocks/leads';

interface CloseLeadDialogProps {
  open: boolean;
  lead: Lead;
  alertMessage?: string;
  dialogTitle: string;
  cancelLabel: string;
  confirmWithoutContactLabel: string;
  confirmWithContactLabel: string;
  onClose: () => void;
  onConfirmWithoutContact: () => void;
  onConfirmWithContact: () => void;
}

export default function CloseLeadDialog({
  open,
  lead,
  alertMessage,
  dialogTitle,
  cancelLabel,
  confirmWithoutContactLabel,
  confirmWithContactLabel,
  onClose,
  onConfirmWithoutContact,
  onConfirmWithContact,
}: CloseLeadDialogProps) {
  const { handleChangeTab } = useTabsMethods();
  const [loadingId, setLoadingId] = useState<string | null>(null);

  const hasDup = !!lead.duplicateContacts?.length;
  const rows = useMemo(
    () =>
      lead.duplicateContacts?.map((dc) => ({
        ...dc,
        id: String(dc.id),
      })) ?? [],
    [lead.duplicateContacts]
  );

  const columns = useMemo<ColumnDef<Record<string, any>>[]>(
    () => [
      { key: 'id', label: 'ID', width: 80 },
      { key: 'nome', label: 'Nome' },
      { key: 'email', label: 'Email' },
      { key: 'telefone', label: 'Telefone' },
      { key: 'telemovel', label: 'Telemóvel' },
      { key: 'nif', label: 'NIF' },
      { key: 'criadoPor', label: 'Criado por' },
      {
        key: 'view',
        label: '',
        width: 56,
        align: 'right',
        render: (_val, row) => {
          const isLoading = loadingId === row.id;
          return (
            <IconButton
              disabled={isLoading}
              onClick={async () => {
                setLoadingId(row.id);
                await handleChangeTab({
                  id: `/dashboard/contacts/users/${row.id}`,
                  title: row.nome,
                });
              }}
            >
              {isLoading ? <CircularProgress size={24} /> : <Visibility fontSize="small" />}
            </IconButton>
          );
        },
      },
    ],
    [handleChangeTab, loadingId]
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        {dialogTitle} ({lead.id})
      </DialogTitle>

      <DialogContent dividers>
        {hasDup && alertMessage && (
          <Stack spacing={2}>
            <Alert severity="info">{alertMessage}</Alert>
            <TableGeneric rows={rows} columns={columns} pagination={false} />
          </Stack>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button variant="outlined" onClick={onClose}>
          {cancelLabel}
        </Button>
        <Box>
          <Button
            variant="contained"
            onClick={onConfirmWithoutContact}
            sx={{ mr: 1 }}
            startIcon={<CheckCircleRounded />}
          >
            {confirmWithoutContactLabel}
          </Button>
          <Button
            variant="contained"
            onClick={onConfirmWithContact}
            startIcon={<CheckCircleRounded />}
          >
            {confirmWithContactLabel}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
}
