'use client';

import { useState, useMemo } from 'react';
import DataTable from '@/components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';

import { Lead } from '@/lib/mocks/leads';
import AssignLeadDialog from './AssignLeadDialog';
import StatusChip, { Status } from '@/components/ui/StatusChip';
import RowActions, { ActionItem } from '@/components/ui/RowActions';
import { TABNAV } from '@/constants/enums';
import { useTranslations } from 'next-intl';
import { AssignmentInd, Cancel, CancelRounded, CheckCircle, Visibility } from '@mui/icons-material';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import ReasonDialog from './ReasonDialog';
import { FailOption } from './types';
import CloseLeadDialog from './CloseLeadDialog';

interface LeadsTableProps {
  leads: Lead[];
}

export default function LeadsTable({ leads }: LeadsTableProps) {
  const t = useTranslations('dashboard.crm.leads');
  const { handleChangeTab } = useTabsMethods();
  useTabsStaticInitializer();

  const [leadToAssign, setLeadToAssign] = useState<Lead | null>(null);
  const [failLead, setFailLead] = useState<Lead | null>(null);
  const [openFailDialog, setOpenFailDialog] = useState(false);
  const [closeLead, setCloseLead] = useState<Lead | null>(null);
  const [openCloseDialog, setOpenCloseDialog] = useState(false);
  const onFailAction = (lead: Lead) => {
    setFailLead(lead);
    setOpenFailDialog(true);
  };

  const onCloseSuccessAction = (lead: Lead) => {
    setCloseLead(lead);
    setOpenCloseDialog(true);
  };

  const failOptions: FailOption[] = useMemo(
    () => [
      {
        value: 'incontactavel',
        label: 'Incontactável, telefone não atribuído e email devolvido',
      },
      {
        value: 'repetida_mesmo_curso',
        label: 'Repetida, vários pedidos para o mesmo curso',
      },
      {
        value: 'repetida_cursos_diferentes',
        label: 'Repetida, mais que dois pedidos para cursos diferentes',
      },
    ],
    []
  );

  const handleFailSubmit = async (reason: string, description: string) => {
    setOpenFailDialog(false);
    // TODO: chamar API para fechar lead sem sucesso
    // await closeLeadApi(failLead!.id, false, { reason, description });
    setFailLead(null);
  };

  async function handleCloseWithoutContact() {
    setOpenCloseDialog(false);
    handleChangeTab({
      title: TABNAV.OPPORTUNITIES,
      id: `/dashboard/crm/opportunities/new`,
    });
  }

  const getLeadActions = (lead: Lead): ActionItem[] => [
    {
      label: t('table.actions.view'),
      icon: <Visibility />,
      onClick: () =>
        handleChangeTab({
          id: `/dashboard/crm/${TABNAV.LEADS}/${lead.id}`,
          title: lead.nome,
        }),
    },
    {
      label: t('table.actions.assign'),
      icon: <AssignmentInd />,
      onClick: () => setLeadToAssign(lead),
    },
    {
      label: t('table.actions.closeSuccess'),
      icon: <CheckCircle color="success" />,
      color: 'success',
      onClick: () => onCloseSuccessAction(lead),
    },
    {
      label: t('table.actions.closeFail'),
      icon: <Cancel color="error" />,
      color: 'error',
      onClick: () => onFailAction(lead),
    },
  ];

  const baseColumns = useMemo<ColumnDef<Lead>[]>(
    () => [
      {
        accessorKey: 'id',
        header: t('table.columns.id'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'estado',
        header: t('table.columns.status'),
        minSize: 128,
        meta: {
          filterType: 'select',
          options: [
            { value: 'new', label: t('table.statusOptions.new') },
            { value: 'assigned', label: t('table.statusOptions.assigned') },
            { value: 'inQualification', label: t('table.statusOptions.inQualification') },
            { value: 'closedSuccess', label: t('table.statusOptions.closedSuccess') },
            { value: 'closedFail', label: t('table.statusOptions.closedFail') },
            { value: 'inValidation', label: t('table.statusOptions.inValidation') },
            { value: 'cancelled', label: t('table.statusOptions.cancelled') },
          ],
        },
        cell: (ctx) => <StatusChip status={ctx.getValue<Status>()} />,
      },
      {
        accessorKey: 'createdAt',
        header: t('table.columns.createdAt'),
        meta: { filterType: 'date' },
        minSize: 128,
        cell: (ctx) =>
          new Date(ctx.getValue<string>()).toLocaleString('pt-PT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }),
        filterFn: (row, id, filterValue: string) =>
          row.getValue<string>(id).startsWith(filterValue),
      },
      {
        accessorKey: 'nome',
        header: t('table.columns.name'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'email',
        header: t('table.columns.email'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'contato',
        header: t('table.columns.contact'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'polo',
        header: t('table.columns.campus'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'curso',
        header: t('table.columns.course'),
        meta: { filterType: 'text' },
        size: 128,
      },
      {
        accessorKey: 'atribuido',
        header: t('table.columns.assignedTo'),
        meta: { filterType: 'text' },
        size: 128,
      },
    ],
    [t]
  );

  const columns = useMemo<ColumnDef<Lead>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => <RowActions actions={getLeadActions(row.original)} />,
      },
    ],
    [baseColumns]
  );

  const handleRowClick = (row: Lead) => {
    /* clique na linha opcional */
    console.info('clicou em', row);
  };

  return (
    <>
      <DataTable<Lead>
        data={leads}
        columns={columns}
        onRowClick={handleRowClick}
        enableFilters
        showTabs
      />

      {leadToAssign && (
        <AssignLeadDialog
          lead={leadToAssign}
          open
          onClose={() => setLeadToAssign(null)}
          dialogTitle={t('assignLeadDialog.dialogTitle')}
          alertMessage={t('assignLeadDialog.alertMessage')}
          instructionText={t('assignLeadDialog.instructionText')}
          searchPlaceholder={'dashboard.crm.leads.assignLeadDialog.searchPlaceholder'}
          confirmLabel={t('assignLeadDialog.confirmLabel')}
          cancelLabel={t('assignLeadDialog.cancelLabel')}
        />
      )}

      {failLead && (
        <ReasonDialog
          open={openFailDialog}
          leadId={failLead.id}
          options={failOptions}
          onClose={() => setOpenFailDialog(false)}
          onSubmit={handleFailSubmit}
          confirmIcon={<CancelRounded />}
          confirmLabel={t('table.actions.closeFail')}
        />
      )}
      {closeLead && (
        <CloseLeadDialog
          open={openCloseDialog}
          lead={closeLead}
          dialogTitle={t('closeDialog.dialogTitle', { id: closeLead.id })}
          alertMessage={t('closeDialog.alertMessage')}
          cancelLabel={t('closeDialog.cancelLabel')}
          confirmWithoutContactLabel={t('closeDialog.confirmWithoutContact')}
          confirmWithContactLabel={t('closeDialog.confirmWithContact')}
          onClose={() => setOpenCloseDialog(false)}
          onConfirmWithoutContact={handleCloseWithoutContact}
          onConfirmWithContact={() => {
            setOpenCloseDialog(false);
            handleChangeTab({
              title: closeLead.nome,
              id: `/dashboard/crm/opportunities/${closeLead.id}`,
            });
          }}
        />
      )}
    </>
  );
}
