'use server';

import { fetchCenters, fetchContactUsers } from '../../../../../../../lib/api/contactUsers';
import { fetchEmployerEntities } from '../../../../../../../lib/api/entities';
import { mockLeads } from '../../../../../../../lib/mocks/leads';
import { NewOpportunitiesPage } from '../new/_components/newOpportunitiesClient';
import { EditOpportunitiesPage } from '../[opportunityId]/_components/EditOpportunitiesClient';
import { getQueryClient } from '../../../../../../../utils';
import { RQHydrate } from '../../../../../../../lib/RQHydrate';
import { Suspense } from 'react';
import { mockOpportunities } from '../../../../../../../lib/mocks/opportunities';
import { VIEW } from '../../../../../../../constants/enums';

export async function OpportunitiesFormServer({
  readOnly = false,
  opportunityId,
}: {
  readOnly?: boolean;
  opportunityId?: string;
}) {
  const isNew = !opportunityId;

  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: ['centers'],
    queryFn: fetchCenters,
  });

  await queryClient.prefetchQuery({
    queryKey: ['contactUsers'],
    queryFn: fetchContactUsers,
  });

  await queryClient.prefetchQuery({
    queryKey: ['entitiesOptions'],
    queryFn: fetchEmployerEntities,
  });

  await queryClient.prefetchQuery({
    queryKey: ['leads'],
    queryFn: () => Promise.resolve(mockLeads),
  });

  if (opportunityId) {
    await queryClient.prefetchQuery({
      queryKey: ['opportunities', opportunityId],
      queryFn: () => Promise.resolve(mockOpportunities.find((o) => o.id == opportunityId)),
    });
  }

  return (
    <RQHydrate>
      <Suspense>
        {isNew ? (
          <NewOpportunitiesPage />
        ) : (
          <EditOpportunitiesPage defaultView={readOnly ? VIEW.VIEW : VIEW.EDIT} />
        )}
      </Suspense>
    </RQHydrate>
  );
}
