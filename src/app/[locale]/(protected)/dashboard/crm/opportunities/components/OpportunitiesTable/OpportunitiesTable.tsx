'use client';

import { useMemo, useState } from 'react';
import DataTable from '@/components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import StatusChip, { Status } from '@/components/ui/StatusChip';
import { Opportunity } from '@/lib/mocks/opportunities';
import RowActions, { ActionItem } from '@/components/ui/RowActions';

import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';

import { TABNAV } from '@/constants/enums';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { useTranslations } from 'next-intl';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import { Delete } from '@mui/icons-material';
interface OpportunitiesTableProps {
  opportunities: Opportunity[];
}

export default function OpportunitiesTable({ opportunities }: OpportunitiesTableProps) {
  useTabsStaticInitializer();
  const { handleChangeTab } = useTabsMethods();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedOpp, setSelectedOpp] = useState<Opportunity | null>(null);
  const t = useTranslations('dashboard.crm.opportunities');

  const getOpportunityActions = (op: Opportunity): ActionItem[] => [
    {
      label: t('actions.view'),
      icon: <VisibilityIcon />,
      onClick: () =>
        handleChangeTab({
          id: `/dashboard/crm/${TABNAV.OPPORTUNITIES}/${op.id}`,
          title: op.nome,
        }),
    },
    {
      label: t('actions.delete'),
      icon: <DeleteIcon />,
      color: 'error',
      sx: { '& .MuiListItemText-primary': { color: 'error.main' } },
      onClick: () => onDeleteAction(op),
    },
  ];

  const onDeleteAction = (op: Opportunity) => {
    setSelectedOpp(op);
    setOpenDeleteDialog(true);
  };

  // lógica de exclusão
  const handleDeleteConfirm = async () => {
    if (!selectedOpp) return;
    setOpenDeleteDialog(false);
    // TODO: chamar API para eliminar oportunidade
    // await deleteOpportunityApi(selectedOpp.id);
  };

  const baseColumns = useMemo<ColumnDef<Opportunity>[]>(
    () => [
      { accessorKey: 'id', header: 'ID', meta: { filterType: 'text' }, minSize: 128 },
      {
        accessorKey: 'estado',
        header: 'Estado',
        minSize: 128,
        meta: {
          filterType: 'select',
          options: [
            { value: 'Nova', label: 'Nova' },
            { value: 'Atribuída', label: 'Atribuída' },
            { value: 'Em qualificação', label: 'Em qualificação' },
            { value: 'Fechada c/ sucesso', label: 'Fechada c/ sucesso' },
            { value: 'Fechada s/ sucesso', label: 'Fechada s/ sucesso' },
            { value: 'Em validação', label: 'Em validação' },
            { value: 'Anulada', label: 'Anulada' },
          ],
        },
        cell: (ctx) => <StatusChip status={ctx.getValue<Status>()} />,
      },
      {
        accessorKey: 'createdAt',
        header: 'Data criação',
        meta: { filterType: 'date' },
        minSize: 128,
        cell: (ctx) =>
          new Date(ctx.getValue<string>()).toLocaleString('pt-PT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }),
        filterFn: (row, id, filterValue: string) =>
          row.getValue<string>(id).startsWith(filterValue),
      },
      { accessorKey: 'nome', header: 'Nome', meta: { filterType: 'text' }, minSize: 128 },
      { accessorKey: 'email', header: 'Email', meta: { filterType: 'text' }, minSize: 128 },
      { accessorKey: 'contato', header: 'Contacto', meta: { filterType: 'text' }, minSize: 128 },
      {
        accessorKey: 'polo',
        header: 'Polo atribuição',
        meta: { filterType: 'text' },
        minSize: 128,
      },
      { accessorKey: 'pagamento', header: 'Pagamento', meta: { filterType: 'text' }, size: 128 },
      { accessorKey: 'atribuido', header: 'Atribuído a', meta: { filterType: 'text' }, size: 128 },
    ],
    []
  );

  const columns = useMemo<ColumnDef<Opportunity>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => <RowActions actions={getOpportunityActions(row.original)} />,
      },
    ],
    [baseColumns]
  );

  const handleRowClick = (row: Opportunity) => {
    console.info('clicou em', row);
  };

  return (
    <>
      <DataTable<Opportunity>
        data={opportunities}
        columns={columns}
        onRowClick={handleRowClick}
        enableFilters
        showTabs
      />
      <ConfirmDialog
        open={openDeleteDialog}
        onCancel={() => setOpenDeleteDialog(false)}
        title={t('dialog.confirmDeleteTitle')}
        subtitle={t('dialog.confirmDeleteSubtitle')}
        description={t('dialog.deleteDescription')}
        onConfirm={handleDeleteConfirm}
        confirmLabel={t('dialog.delete')}
        cancelLabel={t('dialog.cancel')}
        confirmColor="error"
        confirmIcon={<Delete />}
      />
    </>
  );
}
