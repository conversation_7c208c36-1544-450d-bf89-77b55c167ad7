import { UseFormReturn } from 'react-hook-form';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { Search } from '@mui/icons-material';
import {
  opportunityStateOptions,
  useOpportunityTypesOptions,
} from '../../../../../../../../schemas/opportunitySchema';
import ComposableFormWrapper from '../../../../../../../../components/ui/FormWrapper';
import { VIEW } from '../../../../../../../../constants/enums';

export function EditDetailsForm({
  contactsOptions,
  entitiesOptions,
  leadOptions,
  methods,
  disabled,
  onSubmit,
}: {
  contactsOptions: FieldOption[];
  entitiesOptions: FieldOption[];
  leadOptions: FieldOption[];
  disabled?: boolean;
  methods: UseFormReturn<any>;
  onSubmit: React.FormEventHandler<HTMLFormElement>;
}) {
  const opportunityTypesOptions = useOpportunityTypesOptions();

  return (
    <ComposableFormWrapper
      onSubmit={onSubmit}
      view={disabled ? VIEW.VIEW : VIEW.EDIT}
      columns={2}
      methods={methods}
    >
      <ComposableForm.Field
        name="description"
        label="dashboard.crm.opportunities.new.detailsFields.description.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.description.placeholder"
        required
        type="text"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="contact"
        label="dashboard.crm.opportunities.new.detailsFields.contact.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.contact.placeholder"
        options={contactsOptions}
        icon={<Search />}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="type"
        label="dashboard.crm.opportunities.new.detailsFields.type.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.type.placeholder"
        required
        options={opportunityTypesOptions}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="entity"
        label="dashboard.crm.opportunities.new.detailsFields.entity.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.entity.placeholder"
        options={entitiesOptions}
        icon={<Search />}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="assignedTo"
        label="dashboard.crm.opportunities.new.detailsFields.assignedTo.label"
        disableClearable={false}
        icon={<Search />}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="state"
        label="dashboard.crm.opportunities.new.detailsFields.state.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.state.placeholder"
        options={opportunityStateOptions}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="stateDate"
        label="dashboard.crm.opportunities.new.detailsFields.stateDate.label"
        type="date"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="closeDate"
        label="dashboard.crm.opportunities.new.detailsFields.closeDate.label"
        type="date"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="resultState"
        label="dashboard.crm.opportunities.new.detailsFields.resultState.label"
        type="select"
        options={opportunityStateOptions}
        disabled={disabled}
      />
      <ComposableForm.Field
        name="resultReason"
        label="dashboard.crm.opportunities.new.detailsFields.resultReason.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.resultReason.placeholder"
        type="text"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="resultDescription"
        label="dashboard.crm.opportunities.new.detailsFields.resultDescription.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.resultDescription.placeholder"
        type="text"
        multiline
        colSpan={12}
        disabled={disabled}
      />
      <ComposableForm.Field
        name="createdBy"
        label="dashboard.crm.opportunities.new.detailsFields.createdBy.label"
        type="text"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="createdAt"
        label="dashboard.crm.opportunities.new.detailsFields.createdAt.label"
        type="date"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="sourceLead"
        label="dashboard.crm.opportunities.new.detailsFields.sourceLead.label"
        type="select"
        options={leadOptions}
        disabled={disabled}
      />
      <ComposableForm.Field
        name="observations"
        label="dashboard.crm.opportunities.new.detailsFields.observations.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.observations.placeholder"
        type="text"
        multiline
        colSpan={12}
        disabled={disabled}
      />
    </ComposableFormWrapper>
  );
}
