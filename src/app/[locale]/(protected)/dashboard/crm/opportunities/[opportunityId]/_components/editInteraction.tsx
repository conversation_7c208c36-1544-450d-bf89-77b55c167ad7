'use client';

import { interactionMocks } from '../../../../../../../../lib/mocks/interactions';
import FormBuilderDialog, {
  LoadingDialog,
} from '../../../../../../../../components/ui/dialog/FormBuilderDialog';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { useForm, useFormContext, useWatch } from 'react-hook-form';
import { DescriptionField } from '../../../../../../../../components/form-builder/presets/descriptionField';
import { useMutation, useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { InteractionTableType } from '../../../../../../../../schemas/interactionSchema';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { mapLabel } from '../../../../../../../../utils';
import {
  opportunityStateOptions,
  useInteractionStateOptions,
  useInteractionTypesOptions,
} from '../../../../../../../../schemas/opportunitySchema';
import { Suspense } from 'react';

const tPrefix = (x: string) => `dashboard.crm.opportunities.interactions.edit.${x}`;

const closingResultOptions: FieldOption[] = mapLabel(['success', 'insuccess']);

export function OpportunityInteractionFormFields({
  edit = false,
  loading = false,
}: {
  edit?: boolean;
  loading?: boolean;
}) {
  const { control } = useFormContext();

  const [opportunityState] = useWatch({
    control,
    name: ['opportunityState'],
  });

  const interactionTypesOptions = useInteractionTypesOptions();
  const interactionStates = useInteractionStateOptions();

  return (
    <>
      <DescriptionField loading={loading} />
      <ComposableForm.Field
        type="select"
        placeholder={tPrefix('fields.type.placeholder')}
        label={tPrefix('fields.type.label')}
        name={'type'}
        options={interactionTypesOptions}
        loading={loading}
      />
      <ComposableForm.Field
        type="select"
        options={interactionStates}
        placeholder={tPrefix('fields.state.placeholder')}
        label={tPrefix('fields.state.label')}
        name={'state'}
        loading={loading}
      />
      <ComposableForm.Field
        type="datetime"
        label={tPrefix('fields.scheduledDate.label')}
        name={'scheduledDate'}
        loading={loading}
      />
      <ComposableForm.Field
        type="datetime"
        label={tPrefix('fields.doneDate.label')}
        name={'doneDate'}
        loading={loading}
      />
      <ComposableForm.Field
        name={'text'}
        type={'text'}
        label={tPrefix('fields.text.label')}
        placeholder={tPrefix('fields.text.placeholder')}
        multiline
        loading={loading}
      />
      <ComposableForm.Field
        name={'opportunityState'}
        disableClearable={false}
        label={tPrefix('fields.opportunityState.label')}
        placeholder={tPrefix('fields.opportunityState.placeholder')}
        options={opportunityStateOptions}
        type={'select'}
        loading={loading}
      />
      {opportunityState === 'P2 + 3B' && (
        <>
          <ComposableForm.Field
            name={'closingResult'}
            label={tPrefix('fields.closingResult.label')}
            placeholder={tPrefix('fields.closingResult.placeholder')}
            options={closingResultOptions}
            type={'select'}
          />
          <ComposableForm.Field
            name={'closingDate'}
            label={tPrefix('fields.closingDate.label')}
            type={'datetime'}
          />
          <ComposableForm.Field
            name={'closingDescription'}
            label={tPrefix('fields.closingDescription.label')}
            type={'text'}
            multiline
          />
        </>
      )}
      {edit && (
        <>
          <ComposableForm.Field
            name={'doneBy'}
            label={tPrefix('fields.doneBy.label')}
            type={'text'}
            loading={loading}
            disabled
          />
          <ComposableForm.Field
            name={'createdAt'}
            label={tPrefix('fields.createdAt.label')}
            type={'text'}
            loading={loading}
            disabled
          />
        </>
      )}
    </>
  );
}

type NewInteractionDialogProps = {
  onClose: () => any;
  opportunityId: string;
};

function NewInteractionDialogInternal({ onClose, opportunityId }: NewInteractionDialogProps) {
  const methods = useForm<InteractionTableType>();

  const client = useQueryClient();
  const t = useTranslations();

  const { mutate: addInteraction } = useMutation({
    mutationFn: async (interactionInput: InteractionTableType) => {
      void interactionInput;
      //todo add interaction
    },
    onSuccess: () => {
      client.invalidateQueries({
        queryKey: ['opportunities', opportunityId, 'interactions'],
      });
    },
  });

  const onSubmit = methods.handleSubmit((input) => addInteraction(input));

  return (
    <ComposableForm.Provider methods={methods} columns={1}>
      <FormBuilderDialog
        open={true}
        onConfirm={() => onSubmit().then(onClose)}
        onCancel={() => {
          onClose();
        }}
        title={t('dashboard.crm.opportunities.interactions.add')}
      >
        <OpportunityInteractionFormFields />
      </FormBuilderDialog>
    </ComposableForm.Provider>
  );
}

export function NewInteractionDialog(props: NewInteractionDialogProps) {
  const t = useTranslations();

  return (
    <Suspense
      fallback={
        <LoadingDialog
          onCancel={props.onClose}
          title={t('dashboard.crm.opportunities.interactions.add')}
        />
      }
    >
      <NewInteractionDialogInternal {...props} />
    </Suspense>
  );
}

type EditInteractionDialogProps = {
  interactionId: string;
  opportunityId: string;
  onClose: () => any;
};

function EditInteractionDialogInternal({
  interactionId,
  opportunityId,
  onClose,
}: EditInteractionDialogProps) {
  const t = useTranslations();

  const client = useQueryClient();

  const { data } = useSuspenseQuery<InteractionTableType>({
    queryKey: ['opportunities', opportunityId, 'interactions', interactionId],
    queryFn: () => interactionMocks.find((i) => i.id == interactionId)!,
  });

  const { mutate: editInteraction } = useMutation({
    mutationFn: async (interactionInput: InteractionTableType) => {
      void interactionInput;
      //todo add interaction
    },
    onSuccess: () => {
      client.invalidateQueries({
        queryKey: ['opportunities', opportunityId, 'interactions', interactionId],
      });
    },
  });

  const methods = useForm<InteractionTableType>({
    defaultValues: data,
  });

  const onSubmit = methods.handleSubmit((input) => editInteraction(input));

  return (
    <ComposableForm.Provider methods={methods} columns={1}>
      <FormBuilderDialog
        open={true}
        onCancel={() => {
          onClose();
        }}
        onConfirm={() => {
          onSubmit();
          onClose();
        }}
        title={t('dashboard.crm.opportunities.interactions.editDialog', {
          id: interactionId,
        })}
      >
        <OpportunityInteractionFormFields edit />
      </FormBuilderDialog>
    </ComposableForm.Provider>
  );
}

export function EditInteractionDialog(props: EditInteractionDialogProps) {
  const t = useTranslations();

  return (
    <Suspense
      fallback={<LoadingDialog title={t('dashboard.crm.opportunities.interactions.editDialog')} />}
    >
      <EditInteractionDialogInternal {...props} />
    </Suspense>
  );
}
