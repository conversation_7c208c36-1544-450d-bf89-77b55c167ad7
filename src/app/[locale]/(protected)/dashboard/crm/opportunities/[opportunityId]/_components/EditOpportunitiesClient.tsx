'use client';

import { useForm } from 'react-hook-form';
import { SectionCardInternal, SectionTab } from '../../../../../../../../components/ui/SectionCard';
import { useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import { ContactUserType } from '../../../../../../../../types/contactUser';
import { OpportunityEdit } from '../../../../../../../../schemas/opportunitySchema';
import { useToast } from '../../../../../../../../components/ui/ToastProvider';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { EnrollmentForm } from '../../new/_components/enrollmentForm';
import { EditDetailsForm } from './editDetailsForm';
import { InteractionTab } from './interactionTab';
import { Lead } from '../../../../../../../../lib/mocks/leads';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useTabStore } from '../../../../../../../../store/tabStore';
import { VIEW } from '../../../../../../../../constants/enums';
import { useTabsDynamicInitializer } from '../../../../../../../../hooks/useTabs';
import { pathWithoutLocale } from '../../../../../../../../utils';

enum EditOpportunityTab {
  DETAILS,
  DETAILS_ENROLLMENT,
  INTERACTION,
}

export function EditOpportunitiesPage({ defaultView }: { defaultView: VIEW.EDIT | VIEW.VIEW }) {
  const [selectedTab, setSelectedTab] = useState<EditOpportunityTab>(EditOpportunityTab.DETAILS);

  const { data: entitiesOptions } = useSuspenseQuery<FieldOption[]>({
    queryKey: ['entitiesOptions'],
  });

  const { data: contacts } = useSuspenseQuery<ContactUserType[]>({
    queryKey: ['contactUsers'],
  });

  const { data: leads } = useSuspenseQuery<Lead[]>({
    queryKey: ['leads'],
  });

  const opportunityId = useParams<{ opportunityId: string }>().opportunityId;

  const { data: centers } = useSuspenseQuery<string[]>({
    queryKey: ['centers'],
  });

  const { data: opportunity } = useSuspenseQuery<OpportunityEdit>({
    queryKey: ['opportunities', opportunityId],
  });

  const view = useTabStore((state) => state.openTab()?.view || defaultView);

  useTabsDynamicInitializer({
    view,
    location: pathWithoutLocale(usePathname()),
    initial: null,
  });

  const t = useTranslations();
  const router = useRouter();
  const { showToast } = useToast();

  const disabled = view === VIEW.VIEW;

  const methods = useForm<OpportunityEdit>({
    defaultValues: opportunity,
  });

  const contactsOptions = useMemo<FieldOption[]>(
    () =>
      contacts.map((c) => ({
        label: c.name || '',
        value: c.id,
      })),
    [contacts]
  );

  const centersOptions = useMemo(
    () =>
      centers.map((c) => ({
        label: c,
        value: c,
      })),
    [centers]
  );

  const leadOptions = useMemo<FieldOption[]>(
    () =>
      leads.map((l) => ({
        value: l.id,
        label: l.nome,
      })),
    [leads]
  );

  const tabs = useMemo<SectionTab[]>(
    () => [
      {
        label: 'forms.subtab.details',
        value: EditOpportunityTab.DETAILS,
      },
      {
        label: 'forms.subtab.detailsEnrollment',
        value: EditOpportunityTab.DETAILS_ENROLLMENT,
      },
      {
        label: 'forms.subtab.interactions',
        value: EditOpportunityTab.INTERACTION,
      },
    ],
    []
  );

  const editOpportunityHandler = methods.handleSubmit(async (payload) => {
    try {
      void payload;
      //todo call backend

      showToast(t('dashboard.crm.opportunities.edit.successMessage'), 'success');

      router.push('/dashboard/crm/opportunities');
    } catch (err: any) {
      console.error(err);
      showToast(err.toString(), 'error');
    }
  });

  return (
    <>
      <SectionCardInternal
        addSpacer
        tabs={tabs}
        selectedTab={selectedTab}
        onSelectTab={setSelectedTab}
      >
        {selectedTab === EditOpportunityTab.DETAILS && (
          <EditDetailsForm
            contactsOptions={contactsOptions}
            entitiesOptions={entitiesOptions}
            leadOptions={leadOptions}
            methods={methods}
            disabled={disabled}
            onSubmit={editOpportunityHandler}
          />
        )}
        {selectedTab === EditOpportunityTab.DETAILS_ENROLLMENT && (
          <EnrollmentForm centersOptions={centersOptions} disabled={disabled} methods={methods} />
        )}
        {selectedTab === EditOpportunityTab.INTERACTION && (
          <InteractionTab opportunityId={opportunityId} />
        )}
      </SectionCardInternal>
    </>
  );
}
