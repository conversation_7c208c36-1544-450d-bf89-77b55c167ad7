'use client';

import { ColumnDef } from '@tanstack/react-table';
import DataTable from '../../../../../../../../components/ui/DataTable';
import { interactionMocks } from '../../../../../../../../lib/mocks/interactions';
import { InteractionTableType } from '../../../../../../../../schemas/interactionSchema';
import { useCallback, useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import RowActions from '../../../../../../../../components/ui/RowActions';
import { Add, Delete, Edit } from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { EditInteractionDialog, NewInteractionDialog } from './editInteraction';
import { Button, SxProps, Typography } from '@mui/material';

const columnNames = ['description', 'type', 'scheduledDate', 'doneDate', 'doneBy'];

function AddInteractionButton({ onClick, sx = {} }: { sx?: SxProps; onClick: () => any }) {
  const t = useTranslations('dashboard.crm.opportunities.interactions');

  return (
    <Button sx={{ my: '1rem', ...sx }} onClick={onClick} startIcon={<Add />}>
      {t('add')}
    </Button>
  );
}

export function InteractionTab({ opportunityId }: { opportunityId: string }) {
  const t = useTranslations('dashboard.crm.opportunities.interactions');

  const [interactionToEdit, setInteractionToEdit] = useState<string | null>(null);
  const [newInteractionOpen, setNewInteractionOpen] = useState(false);

  const client = useQueryClient();

  const { mutate: deleteInteraction } = useMutation({
    mutationFn: async (interactionId: string) => {
      void interactionId;
      //todo delete interaction
    },
    onSuccess: (_, interactionId) => {
      client.invalidateQueries({
        queryKey: ['opportunities', opportunityId, 'interactions', interactionId],
      });
    },
  });

  const columns = useMemo<ColumnDef<InteractionTableType>[]>(
    () => [
      ...columnNames.map((n) => ({
        accessorKey: n,
        header: t(`columns.${n}`),
      })),

      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions
            actions={[
              {
                icon: <Edit />,
                label: t('editItem'),
                onClick: () => setInteractionToEdit(row.original.id),
              },
              {
                icon: <Delete />,
                label: t('deleteItem'),
                onClick: () => deleteInteraction(row.original.id),
              },
            ]}
          />
        ),
      },
    ],
    [t]
  );

  const openNewDialog = useCallback(() => setNewInteractionOpen(true), []);

  return (
    <>
      {interactionMocks.length ? (
        <>
          <DataTable
            enableFilters={false}
            data={interactionMocks}
            columns={columns}
            onRowClick={() => null}
          />
          <AddInteractionButton sx={{ alignSelf: 'flex-start' }} onClick={openNewDialog} />
        </>
      ) : (
        <>
          <Typography>{t('noneExisting')}</Typography>
          <AddInteractionButton onClick={openNewDialog} />
        </>
      )}
      {interactionToEdit != null && (
        <EditInteractionDialog
          opportunityId={opportunityId}
          interactionId={interactionToEdit}
          onClose={() => setInteractionToEdit(null)}
        />
      )}
      {newInteractionOpen && (
        <NewInteractionDialog
          opportunityId={opportunityId}
          onClose={() => setNewInteractionOpen(false)}
        />
      )}
    </>
  );
}
