'use client';

import { useForm } from 'react-hook-form';
import { SectionCardInternal, SectionTab } from '../../../../../../../../components/ui/SectionCard';
import { useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import { ContactUserType } from '../../../../../../../../types/contactUser';
import { OpportunityInput } from '../../../../../../../../schemas/opportunitySchema';
import { useToast } from '../../../../../../../../components/ui/ToastProvider';
import { useRouter } from 'next/navigation';
import { NewDetailsForm } from './newDetailsForm';
import { EnrollmentForm } from './enrollmentForm';
import { useSuspenseQuery } from '@tanstack/react-query';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';

enum NewOpportunityTab {
  DETAILS,
  DETAILS_ENROLLMENT,
}

export function NewOpportunitiesPage() {
  const [selectedTab, setSelectedTab] = useState<NewOpportunityTab>(NewOpportunityTab.DETAILS);

  const { data: entitiesOptions } = useSuspenseQuery<FieldOption[]>({
    queryKey: ['entitiesOptions'],
  });

  const { data: contacts } = useSuspenseQuery<ContactUserType[]>({
    queryKey: ['contactUsers'],
  });

  const { data: centers } = useSuspenseQuery<string[]>({
    queryKey: ['centers'],
  });

  const t = useTranslations();
  const router = useRouter();
  const { showToast } = useToast();

  const methods = useForm<OpportunityInput>();

  const contactsOptions = useMemo<FieldOption[]>(
    () =>
      contacts.map((c) => ({
        label: c.name || '',
        value: c.id,
      })),
    [contacts]
  );

  const centersOptions = useMemo(
    () =>
      centers.map((c) => ({
        label: c,
        value: c,
      })),
    [centers]
  );

  const tabs = useMemo<SectionTab[]>(
    () => [
      {
        label: 'forms.subtab.details',
        value: NewOpportunityTab.DETAILS,
      },
      {
        label: 'forms.subtab.detailsEnrollment',
        value: NewOpportunityTab.DETAILS_ENROLLMENT,
      },
    ],
    []
  );

  const createOpportunityHandler = methods.handleSubmit(async (payload) => {
    try {
      void payload;
      //todo call backend

      showToast(t('dashboard.crm.opportunities.new.sucessMessage'), 'success');

      router.push('/dashboard/crm/opportunities');
    } catch (err: any) {
      console.error(err);
      showToast(err.toString(), 'error');
    }
  });

  return (
    <SectionCardInternal
      addSpacer
      tabs={tabs}
      selectedTab={selectedTab}
      onSelectTab={setSelectedTab}
    >
      {selectedTab === NewOpportunityTab.DETAILS && (
        <NewDetailsForm
          contactsOptions={contactsOptions}
          entitiesOptions={entitiesOptions}
          methods={methods}
          onSubmit={createOpportunityHandler}
        />
      )}
      {selectedTab === NewOpportunityTab.DETAILS_ENROLLMENT && (
        <EnrollmentForm centersOptions={centersOptions} methods={methods} />
      )}
    </SectionCardInternal>
  );
}
