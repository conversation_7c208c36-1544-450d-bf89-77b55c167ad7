import { UseFormReturn } from 'react-hook-form';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { Search } from '@mui/icons-material';
import { ShiftField } from '../../../../../../../../components/form-builder/presets/shiftField';
import { BaseLearningFields } from '../../../../../../../../components/form-builder/presets/baseLearningFields';

export function EnrollmentForm({
  centersOptions,
  disabled,
  methods,
}: {
  centersOptions: FieldOption[];
  disabled?: boolean;
  methods: UseFormReturn<any>;
}) {
  return (
    <ComposableForm.Provider columns={2} methods={methods}>
      <ComposableForm.Field
        name="course"
        label="dashboard.crm.opportunities.new.enrollmentFields.course.label"
        placeholder="dashboard.crm.opportunities.new.enrollmentFields.course.placeholder"
        icon={<Search />}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="center"
        label="dashboard.crm.opportunities.new.enrollmentFields.center.label"
        placeholder="dashboard.crm.opportunities.new.enrollmentFields.center.placeholder"
        options={centersOptions}
        type="select"
        disabled={disabled}
      />
      <ComposableForm.Field
        name="modality"
        label="dashboard.crm.opportunities.new.enrollmentFields.modality.label"
        placeholder="dashboard.crm.opportunities.new.enrollmentFields.modality.placeholder"
        type="select"
        disabled={disabled}
      />
      <ShiftField disabled={disabled} />
      <BaseLearningFields disabled={disabled} />
    </ComposableForm.Provider>
  );
}
