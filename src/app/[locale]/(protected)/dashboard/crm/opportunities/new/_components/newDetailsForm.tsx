import { UseFormReturn } from 'react-hook-form';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { Search } from '@mui/icons-material';
import ComposableFormWrapper from '../../../../../../../../components/ui/FormWrapper';
import { VIEW } from '../../../../../../../../constants/enums';
import { useOpportunityTypesOptions } from '../../../../../../../../schemas/opportunitySchema';

export function NewDetailsForm({
  contactsOptions,
  entitiesOptions,
  methods,
  onSubmit,
}: {
  contactsOptions: FieldOption[];
  entitiesOptions: FieldOption[];
  disabled?: boolean;
  methods: UseFormReturn<any>;
  onSubmit: React.FormEventHandler<HTMLFormElement>;
}) {
  const opportunityTypesOptions = useOpportunityTypesOptions();
  return (
    <ComposableFormWrapper view={VIEW.CREATE} columns={2} methods={methods} onSubmit={onSubmit}>
      <ComposableForm.Field
        name="description"
        label="dashboard.crm.opportunities.new.detailsFields.description.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.description.placeholder"
        required
        type="text"
      />
      <ComposableForm.Field
        name="contact"
        label="dashboard.crm.opportunities.new.detailsFields.contact.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.contact.placeholder"
        options={contactsOptions}
        icon={<Search />}
        type="select"
      />
      <ComposableForm.Field
        name="type"
        label="dashboard.crm.opportunities.new.detailsFields.type.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.type.placeholder"
        options={opportunityTypesOptions}
        required
        type="select"
      />
      <ComposableForm.Field
        name="entity"
        label="dashboard.crm.opportunities.new.detailsFields.entity.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.entity.placeholder"
        options={entitiesOptions}
        icon={<Search />}
        type="select"
      />
      <ComposableForm.Field
        name="assignedTo"
        label="dashboard.crm.opportunities.new.detailsFields.assignedTo.label"
        disableClearable={false}
        icon={<Search />}
        type="select"
      />
      <ComposableForm.Field
        name="observations"
        label="dashboard.crm.opportunities.new.detailsFields.observations.label"
        placeholder="dashboard.crm.opportunities.new.detailsFields.observations.placeholder"
        type="text"
        multiline
        colSpan={12}
      />
    </ComposableFormWrapper>
  );
}
