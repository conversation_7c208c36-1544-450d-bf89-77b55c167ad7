'use client';

import { useState } from 'react';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import type { ProfileTableRow } from '@/lib/api/profiles';
import ProfilesTable from './components/ProfilesTable';
import { columns } from './components/ProfilesTable/columns';
import { VIEW } from '@/constants/enums';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { Delete, FileCopy } from '@mui/icons-material';
import { useTranslations } from 'next-intl';

type PendingAction = {
  id: string;
  type: 'duplicate' | 'delete';
};

export default function ProfilesClient({ initialRows }: { initialRows: ProfileTableRow[] }) {
  useTabsStaticInitializer();
  const t = useTranslations('dashboard.profiles');
  const [pendingAction, setPendingAction] = useState<PendingAction | null>(null);
  const { handleChangeTab } = useTabsMethods();

  const handleEdit = (id: string) => {
    handleChangeTab({
      id: `/dashboard/profiles/${id}`,
      title: `Perfil ${id}`,
      view: VIEW.EDIT,
    });
  };

  const handleDuplicate = (id: string) => {
    setPendingAction({ id, type: 'duplicate' });
  };

  const handleDelete = (id: string) => {
    setPendingAction({ id, type: 'delete' });
  };

  const handleCancel = () => {
    setPendingAction(null);
  };

  const handleConfirm = () => {
    if (!pendingAction) return;
    console.log(pendingAction.type, pendingAction.id);
    //TODO: Action de duplicate/delete
    setPendingAction(null);
  };

  return (
    <>
      <ProfilesTable
        rows={initialRows}
        onEditAction={handleEdit}
        onDuplicateAction={handleDuplicate}
        onDeleteAction={handleDelete}
        columns={columns}
      />

      <ConfirmDialog
        open={!!pendingAction}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        maxWidth="sm"
        title={
          pendingAction?.type === 'delete' ? t('deleteProfile.title') : t('duplicateProfile.title')
        }
        subtitle={
          pendingAction?.type === 'delete'
            ? t('deleteProfile.subtitle')
            : t('duplicateProfile.subtitle')
        }
        description={
          pendingAction?.type === 'delete'
            ? t('deleteProfile.description')
            : t('duplicateProfile.description')
        }
        cancelLabel={t('buttons.cancel')}
        confirmLabel={
          pendingAction?.type === 'delete' ? t('buttons.delete') : t('buttons.duplicate')
        }
        confirmColor={pendingAction?.type === 'delete' ? 'error' : 'primary'}
        confirmIcon={pendingAction?.type === 'delete' ? <Delete /> : <FileCopy />}
      />
    </>
  );
}
