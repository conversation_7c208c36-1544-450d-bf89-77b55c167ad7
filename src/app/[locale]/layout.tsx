import type { Metadata } from 'next';
import { Providers } from '../providers';
// import { Navbar } from '@/components/ui/Navbar';
import { getMessages, getLocale } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';

export const metadata: Metadata = {
  title: 'Caixa Mágica', // TODO rename?
  description: 'Boilerplate com Next.js 15 + Auth + i18n + MUI',
};

export default async function LocaleLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();

  const messages = await getMessages({ locale });
  const session = await getServerSession(authOptions);

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      {/* <Navbar /> */}
      {/* TODO check if Nav<PERSON> is disabled here */}
      <Providers session={session}>{children}</Providers>
    </NextIntlClientProvider>
  );
}
