'use client';

import {
  <PERSON>,
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Link as MuiLink,
  FormControlLabel,
  Checkbox,
  CircularProgress,
} from '@mui/material';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useActionState, useEffect, useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { loginAction } from '@/app/actions/auth';
import type { LoginState } from '../types';
import { useNextAuthError } from '@/hooks/useNextAuthError';

export default function LoginCardFields() {
  const t = useTranslations();
  const router = useRouter();
  const authError = useNextAuthError();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSigningIn, setIsSigningIn] = useState(false);

  const [state, formAction] = useActionState<LoginState, FormData>(loginAction, {
    errors: {},
  });

  useEffect(() => {
    if (state.success) {
      setIsSigningIn(true);
      (async () => {
        const res = await signIn('credentials', {
          email,
          password,
          redirect: false,
          callbackUrl: '/dashboard',
        });

        setIsSigningIn(false);

        if (res?.ok && res.url) {
          router.replace(res.url);
        }
      })();
    }
  }, [state.success, email, password, router, t]);
  const loading = state.pending || isSigningIn;
  return (
    <Card elevation={4} sx={{ width: '100%', boxShadow: 6 }}>
      <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            mb: 3,
          }}
        >
          <Typography variant="h3" textAlign="center">
            {t('signIn.title')}
          </Typography>
          <Typography variant="body1" textAlign="center">
            {t('signIn.subtitle')}
          </Typography>
        </Box>

        <Box
          component="form"
          action={formAction}
          noValidate
          sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}
        >
          <TextField
            name="email"
            label={t('form.email')}
            type="email"
            fullWidth
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            error={!!state.errors?.email}
            helperText={state.errors?.email}
          />

          <TextField
            name="password"
            label={t('form.password')}
            type="password"
            fullWidth
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            error={!!state.errors?.password || !!authError}
            helperText={state.errors?.password || authError}
          />

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 2,
            }}
          >
            <FormControlLabel
              control={<Checkbox name="rememberMe" color="primary" defaultChecked={false} />}
              label={t('form.rememberMe')}
            />
            <Typography variant="body2" align="right">
              <MuiLink component={Link} href="/forgot-password">
                {t('button.forgotPassword')}
              </MuiLink>
            </Typography>
          </Box>

          <Button type="submit" variant="contained" fullWidth disabled={loading}>
            {loading ? <CircularProgress size={24} color="inherit" /> : t('button.signIn')}
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
}