'use client';

import { useRouter } from 'next/navigation';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Container,
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Link as Mui<PERSON>ink,
  TextField,
  CircularProgress,
} from '@mui/material';
import Link from 'next/link';

import {
  createUserSchema,
  type CreateUserFormData,
  apiCreateUserSchema,
  ApiCreateUserData,
} from '@/schemas/authSchema';
import { useToast } from '@/components/ui/ToastProvider';
import { FormTextField } from '@/components/ui/FormTextField';

import { createUserAction } from '@/app/actions/auth';
import { useTranslations } from 'next-intl';

export default function SignUpPage() {
  const router = useRouter();
  const { showToast } = useToast();
  const t = useTranslations();
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserSchema),
  });

  function formatPhone(value: string) {
    const digits = value.replace(/\D/g, '').slice(0, 11);
    const d = digits;
    if (d.length <= 2) return d;
    if (d.length <= 6) return `(${d.slice(0, 2)}) ${d.slice(2)}`;
    if (d.length <= 10) return `(${d.slice(0, 2)}) ${d.slice(2, 7)}-${d.slice(7)}`;
    return `(${d.slice(0, 2)}) ${d.slice(2, 7)}-${d.slice(7, 11)}`;
  }

  const onSubmit = async (data: CreateUserFormData) => {
    const payload = apiCreateUserSchema.parse({
      name: data.name,
      email: data.email,
      password: data.password,
      phone: data.phone,
    } as ApiCreateUserData);

    try {
      const formData = new FormData();
      Object.entries(payload).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value);
        }
      });
      await createUserAction(payload);

      showToast(t('signUp.successMessage'), 'success');
      router.push('/sign-in');
    } catch (err: unknown) {
      const msg = err instanceof Error ? err.message : 'An unexpected error occurred.';
      showToast(msg, 'error');
    }
  };

  return (
    <Container maxWidth="md">
      <Box
        component="main"
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4,
        }}
      >
        <Card elevation={3} sx={{ width: '100%' }}>
          <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography variant="h5" align="center" fontWeight={600}>
              {t('signUp.subtitle')}
            </Typography>

            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
                  gap: 2,
                }}
              >
                <FormTextField
                  label="Your Name"
                  fullWidth
                  {...register('name')}
                  error={errors.name}
                />
                <FormTextField
                  label="Your Email"
                  type="email"
                  fullWidth
                  {...register('email')}
                  error={errors.email}
                />
                <FormTextField
                  label="Password"
                  type="password"
                  fullWidth
                  {...register('password')}
                  error={errors.password}
                />
                <FormTextField
                  label="Confirm Password"
                  type="password"
                  fullWidth
                  {...register('confirmPassword')}
                  error={errors.confirmPassword}
                />
                <Controller
                  name="phone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Contact Phone"
                      fullWidth
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                      onChange={(e) => {
                        const raw = e.target.value;
                        const formatted = formatPhone(raw);
                        field.onChange(formatted);
                      }}
                      value={field.value || ''}
                    />
                  )}
                />
              </Box>

              <Button
                type="submit"
                variant="contained"
                fullWidth
                sx={{ mt: 4 }}
                endIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : undefined}
                disabled={isSubmitting}
              >
                {isSubmitting ? t('button.SigningUp') : t('button.signUp')}
              </Button>
            </Box>

            <Typography variant="body2" align="center">
              {t('signUp.account')}
              <MuiLink component={Link} href="/sign-in">
                {t('button.signIn')}
              </MuiLink>
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
}
