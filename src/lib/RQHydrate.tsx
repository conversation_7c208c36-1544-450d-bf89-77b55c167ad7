'use server';

import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { getQueryClient } from '../utils';

//this is needed if you want the queryClient instance to be passed down to the client
export async function RQHydrate({ children }: { children: ReactNode }) {
  const queryClient = getQueryClient();

  return <HydrationBoundary state={dehydrate(queryClient)}>{children}</HydrationBoundary>;
}
