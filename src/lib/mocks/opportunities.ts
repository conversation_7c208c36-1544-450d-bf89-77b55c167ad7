export type Opportunity = {
  id: string;
  estado: string;
  createdAt: string;
  nome: string;
  email: string;
  contato: string;
  polo: string;
  pagamento: string;
  atribuido: string;
};

export const mockOpportunities: Opportunity[] = [
  {
    id: '58880',
    estado: 'Nova',
    createdAt: '2025-07-07T18:21:00Z',
    nome: '<PERSON><PERSON><PERSON>',
    email: 'filip<PERSON><EMAIL>',
    contato: '917470173',
    polo: 'Lis<PERSON>',
    pagamento: '€1.500,00',
    atribuido: '<PERSON><PERSON>',
  },
  {
    id: '58881',
    estado: 'Atribuída',
    createdAt: '2025-07-07T18:22:00Z',
    nome: '<PERSON>',
    email: '<EMAIL>',
    contato: '912345678',
    polo: 'Porto',
    pagamento: '€1.200,00',
    atribuido: '<PERSON>',
  },
  {
    id: '58882',
    estado: '<PERSON>chada c/ sucesso',
    createdAt: '2025-07-07T18:25:00Z',
    nome: '<PERSON>',
    email: '<EMAIL>',
    contato: '934567890',
    polo: 'Coimbra',
    pagamento: '€1.800,00',
    atribuido: '<PERSON>a <PERSON>',
  },
  {
    id: '58883',
    estado: 'Fechada s/ sucesso',
    createdAt: '2025-07-07T18:30:00Z',
    nome: 'Diogo Ferraz',
    email: '<EMAIL>',
    contato: '926543210',
    polo: 'Lisboa',
    pagamento: '€1.100,00',
    atribuido: 'Rita Gomes',
  },
  {
    id: '58884',
    estado: 'Anulada',
    createdAt: '2025-07-08T09:10:00Z',
    nome: 'Eduarda Lima',
    email: '<EMAIL>',
    contato: '938887766',
    polo: 'Faro',
    pagamento: '€900,00',
    atribuido: 'Joana Pereira',
  },
  {
    id: '58885',
    estado: 'Em qualificação',
    createdAt: '2025-07-08T09:12:00Z',
    nome: 'Fábio Rocha',
    email: '<EMAIL>',
    contato: '915556677',
    polo: 'Braga',
    pagamento: '€1.300,00',
    atribuido: 'Miguel Azevedo',
  },
  {
    id: '58886',
    estado: 'Em validação',
    createdAt: '2025-07-08T09:15:00Z',
    nome: 'Gabriela Sousa',
    email: '<EMAIL>',
    contato: '917112233',
    polo: 'Lisboa',
    pagamento: '€1.400,00',
    atribuido: 'Rita Gomes',
  },
  {
    id: '58887',
    estado: 'Atribuída',
    createdAt: '2025-07-08T09:17:00Z',
    nome: 'Henrique Alves',
    email: '<EMAIL>',
    contato: '914998877',
    polo: 'Porto',
    pagamento: '€2.000,00',
    atribuido: 'Joana Pereira',
  },
  {
    id: '58888',
    estado: 'Nova',
    createdAt: '2025-07-08T09:20:00Z',
    nome: 'Inês Faria',
    email: '<EMAIL>',
    contato: '931223344',
    polo: 'Coimbra',
    pagamento: '€1.750,00',
    atribuido: 'Miguel Azevedo',
  },
  {
    id: '58889',
    estado: 'Em qualificação',
    createdAt: '2025-07-08T09:25:00Z',
    nome: 'João Costa',
    email: '<EMAIL>',
    contato: '936554433',
    polo: 'Lisboa',
    pagamento: '€2.200,00',
    atribuido: 'Rita Gomes',
  },
];
