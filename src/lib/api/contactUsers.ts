import { ContactUserType, SelectOptions } from '@/types/contactUser';

export async function fetchContactUsers(): Promise<ContactUserType[]> {
  return [
    {
      id: '58880',
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      mobile: '91740173',
      phone: '*********',
      nif: '*********',
      socialSecurityNumber: '*********',
      identificationType: 'Passport',
      identificationValidity: new Date('2030-12-31'),
      identificationNumber: 'X1234567',
      identificationFile: 'cc',
      address: '123 Main Street',
      postalCode: '1000-001',
      locality: 'Lisboa',
      district: 'district-1',
      municipality: 'municipality-1',
      parish: 'parish-1',
      country: 'country-1',
      birthPlace: 'Porto',
      profession: 'Auxiliar de reabilitação',
      employerEntity: '9517532468',
      contractType: 'contractType-1',
      birthDate: new Date('1990-05-15'),
      gender: 'gender-1',
      iban: '*************************',
      educationLevel: 'educationLevel-1',
      notes: 'Test user for demo purposes.',
      rgpd: {
        consent_1: true,
        consent_2: true,
        consent_3: true,
        consent_4: false,
        consent_5: true,
      },
      relationships: {
        '111555': {
          user: 'a1b2c3d4-e5f6-7890-abcd-*********0ef',
          relationshipType: 'yyy',
        },
      },
      entities: [{ id: 'yyyyyyy', name: 'Casapia', description: 'sem descricao' }],
      attachments: {},
      crmHistory: [
        {
          id: 'opportunity-1',
          description: 'xxx',
          type: 'opportunities',
          state: 'eee',
          madeBy: '58881',
          creationDate: new Date('2023-10-01'),
          enrollment: 'Initial contact made.',
        },
        {
          id: 'opportunity-2',
          description: 'yyy',
          type: 'leads',
          state: 'fff',
          madeBy: '58882',
          creationDate: new Date('2023-11-15'),
          enrollment: 'Follow-up email sent.',
        },
        {
          id: 'extra-1',
          description: 'zzz',
          type: 'opportunities-extra',
          state: 'ggg',
          madeBy: '58883',
          creationDate: new Date('2023-12-05'),
          enrollment: 'Scheduled a meeting.',
        },
      ],
      enrollments: {},
    },
    {
      id: 'a1b2c3d4-e5f6-7890-abcd-*********0ef',
      name: 'Jane Smith',
      email: '<EMAIL>',
      mobile: '923456789',
      phone: '223456789',
      nif: '*********',
      socialSecurityNumber: '*********',
      identificationType: 'Citizen Card',
      identificationValidity: new Date('2028-07-20'),
      identificationNumber: 'Y7654321',
      identificationFile: 'cc',
      address: '456 Side Avenue',
      postalCode: '2000-002',
      locality: 'Porto',
      district: 'district-1',
      municipality: 'municipality-1',
      parish: 'parish-1',
      country: 'country-1',
      birthPlace: 'Lisbon',
      profession: 'Product Manager',
      employerEntity: 'Innovatech SA',
      contractType: 'contractType-1',
      birthDate: new Date('1985-11-23'),
      gender: 'gender-1',
      iban: '*************************',
      educationLevel: 'educationLevel-1',
      notes: 'Second test user for demo.',
      rgpd: {
        consent_1: true,
        consent_2: false,
        consent_3: true,
        consent_4: true,
        consent_5: false,
      },
      relationships: {
        '111555': {
          user: 'f92a1c6e-3b47-4f10-bd9c-2b7c5c4a1e9b',
          relationshipType: 'yyy',
        },
      },
      entities: [{ id: 'wwwwwww', name: 'Inova', description: 'sem descricao' }],
      attachments: {},
      crmHistory: [],
      enrollments: {},
    },
  ];
}

export async function fetchContactUserList(): Promise<Array<{ id: string; name: string }>> {
  const users = await fetchContactUsers();
  return users.map((user: ContactUserType) => ({
    id: user.id,
    name: user.name || ' - ',
  }));
}

export async function fetchContactUserOptions(): Promise<SelectOptions> {
  return {
    district: [{ value: 'district-1', label: 'Lisbon' }],
    municipality: [{ value: 'municipality-1', label: 'Lisbon' }],
    parish: [{ value: 'parish-1', label: 'Santo António' }],
    country: [{ value: 'country-1', label: 'Portugal' }],
    contractType: [{ value: 'contractType-1', label: 'Full-time' }],
    gender: [{ value: 'gender-1', label: 'Male' }],
    educationLevel: [{ value: 'educationLevel-1', label: "Master's Degree" }],
    relationshipTypes: [
      { label: 'Pai', value: 'xxx' },
      { label: 'Mãe', value: 'yyy' },
    ],
  };
}

export async function fetchRelationshipType(): Promise<any | null> {
  return [
    { label: 'Pai', value: 'xxx' },
    { label: 'Mãe', value: 'yyy' },
  ];
}

export async function fetchFileTypes(): Promise<any | null> {
  return [
    { label: 'Texto', value: 'text' },
    { label: 'Imagem', value: 'image' },
    { label: 'Pdf', value: 'pdf' },
    { label: 'Excel', value: 'excel' },
  ];
}

export async function fetchCenters() {
  return ['Coimbra', 'Lisboa', 'Faro', 'Braga', 'Porto'];
}
