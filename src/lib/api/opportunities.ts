export interface OpportunitiesObject {
  id: string;
  state: string; //ENUM maybe??
  description: string;
  type: string;
  contactInfo: string;
  entity: string;
  assignedTo: string;
}

export async function fetchOpportunities(): Promise<OpportunitiesObject[]> {
  return [
    {
      id: 'a1b2c3d4',
      state: 'Fechada s/ sucesso',
      description: 'New business lead from web form',
      type: 'Lead',
      contactInfo: '<EMAIL>',
      entity: 'Acme Corp',
      assignedTo: '<PERSON>',
    },
    {
      id: 'e5f6g7h8',
      state: 'P2 + 3B',
      description: 'Follow-up with previous client',
      type: 'Follow-up',
      contactInfo: '<EMAIL>',
      entity: 'Beta LLC',
      assignedTo: '<PERSON>',
    },
    {
      id: 'i9j0k1l2',
      state: 'P1',
      description: 'Em negociação',
      type: 'Contract',
      contactInfo: '<EMAIL>',
      entity: 'Gamma Inc',
      assignedTo: '<PERSON>',
    },
  ];
}