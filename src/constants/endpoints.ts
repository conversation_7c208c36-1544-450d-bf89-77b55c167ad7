const BASE_PUBLIC = process.env.NEXT_API_PUBLIC_BASE_URL!;
const BASE_PRIVATE = process.env.NEXT_API_PRIVATE_BASE_URL!;

const endpoints = {
  AUTH: {
    LOGIN: `${BASE_PUBLIC}/auth/login`,
    SEND_RECOVER_PASS: `${BASE_PUBLIC}/auth/sendRecoverPassword`,
  },
  USERS: {
    BY_EMAIL: `${BASE_PRIVATE}/users/byemail`,
    CREATE: `${BASE_PRIVATE}/users/createuser`,
    GET_ONE: `${BASE_PRIVATE}/users/user`,
    LIST: `${BASE_PRIVATE}/users`,
  },
  ENTITIES: `${BASE_PRIVATE}/entities`,
  ROLES: `${BASE_PRIVATE}/roles`,
  SUBROLES: `${BASE_PRIVATE}/subroles`,
  FUNCTIONALITIES: `${BASE_PRIVATE}/functionalities`,
};

export default endpoints;
