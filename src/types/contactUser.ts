export type CRMHistoryType = {
  category:
    | 'OPPORTUNITY'
    | 'INTERACTION_OPPORTUNITY'
    | 'INTERACTION_SYSTEM'
    | 'INTERACTION_EXTERNAL';
  id: string;
  description: string;
  type: string;
  state: string;
  madeBy: string;
  creationDate: Date;
  enrollment: string;
  extraData: string;
};

export type ContactUserType = {
  id: string;
  name?: string;
  email?: string;
  mobile?: string;
  phone?: string;
  nif?: string;
  socialSecurityNumber?: string;
  identificationType?: string;
  identificationValidity?: Date;
  identificationNumber?: string;
  identificationFile?: string;
  address?: string;
  postalCode?: string;
  locality?: string;
  district?: string;
  municipality?: string;
  parish?: string;
  country?: string;
  birthPlace?: string;
  profession?: string;
  employerEntity?: string;
  contractType?: string;
  birthDate?: Date;
  gender?: string;
  iban?: string;
  educationLevel?: string;
  notes?: string;
  rgpd?: Record<string, boolean>;
  relationships?: any;
  entities?: Array<{ id: string; name: string; description: string }>;
  attachments?: Record<string, unknown>;
  crmHistory?: Array<CRMHistoryType>;
  enrollments?: Record<string, unknown>;
};

export const CONTACT_USER_SUBTAB = {
  DETAILS: 'details',
  RGPD: 'rgpd',
  RELATIONSHIPS: 'relationships',
  ENTITIES: 'entities',
  ATTACHMENTS: 'attachments',
  CRM_HISTORY: 'crmHistory',
  ENROLLMENTS: 'enrollments',
  PAYMENTS: 'payments',
};
export type ContactUserSubTabType = (typeof CONTACT_USER_SUBTAB)[keyof typeof CONTACT_USER_SUBTAB];

export type SelectOption = { value: string; label: string };
export type SelectOptions = {
  district?: SelectOption[];
  municipality?: SelectOption[];
  parish?: SelectOption[];
  country?: SelectOption[];
  contractType?: SelectOption[];
  gender?: SelectOption[];
  educationLevel?: SelectOption[];
  relationshipTypes?: SelectOption[];
  users?: SelectOption[];
  fileTypes?: SelectOption[];
  employer?: SelectOption[];
};
