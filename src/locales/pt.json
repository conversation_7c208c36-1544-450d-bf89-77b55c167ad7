{"hello": "Ol<PERSON>!", "welcome": "Bem-vindo à Caixa Mágica", "subtitle": "Desenvolvendo software human-oriented para o setor público, fintechs e startups.", "success": "Sucesso", "insuccess": "Insucesso", "yes": "<PERSON>m", "no": "Não", "homepage": {"title": "A tua área de formação começa aqui!", "subtitle": "Plataforma digital para formandos, formadores e equipas de gestão - tudo num só lugar."}, "gdpr": "RGPD", "signIn": {"title": "Bem-vindo!", "mobileTitle": "<PERSON><PERSON><PERSON>", "subtitle": "Inicia sessão para acederes à tua área SA Formação"}, "signUp": {"title": "C<PERSON><PERSON> conta", "subtitle": "Crie a sua conta na Caixa Mágica", "account": "Já tem conta? ", "successMessage": "Conta criada com sucesso. Já pode iniciar sessão."}, "forgotPassword": {"title": "Esqueceu a palavra-passe?", "instructions": "Consulte o seu e-mail para redefinir a palavra-passe.", "sentTitle": "Email a caminho!", "sentInstructions": "Enviámos-te um link para repor a palavra-passe. Não recebeste? Verifica a pasta de spam ou tenta novamente dentro de alguns minutos.", "resendLink": "Reenviar link", "backToSignIn": "Voltar para Início de sessão", "ResendLinkIn": "Reenviar email em {time}"}, "resetPassword": {"title": "Redefinir palavra-passe", "newPassword": "Nova palavra-passe", "confirmPassword": "Confirmar palavra-passe", "successMessage": "Palavra-passe alterada com sucesso. Já podes iniciar sessão."}, "button": {"submit": "Enviar", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "Registar", "cancel": "<PERSON><PERSON><PERSON>", "create": "Create", "SigningUp": "A criar conta...", "forgotPassword": "<PERSON><PERSON><PERSON>-me da palavra-passe", "sendReset": "Enviar link de redefinição", "submitting": "A enviar...", "sent": "Enviado", "createEntity": "Criar Entidade"}, "nav": {"en": "EN", "pt": "PT"}, "form": {"email": "Email", "password": "Palavra-passe", "rememberMe": "<PERSON><PERSON><PERSON><PERSON>me"}, "errors": {"invalidCredentials": "Credenciais inválidas. Por favor, tente novamente.", "unexpectedError": "Ocorreu um erro inesperado. Por favor, tente novamente mais tarde.", "recoverPasswordEmailFailed": "Ocorreu um erro ao enviar o e-mail de recuperação."}, "dashboard": {"placeholder": {"new": {"profiles": "Novo perfil", "users": "Novo utilizador", "leads": "Nova lead", "opportunities": "Nova oportunidade", "entities": "Nova entidade"}}, "profiles": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Gere os diferentes perfis de utilizador e defina os seus níveis de acesso.", "label": "Novo perfil", "buttons": {"delete": "Eliminar", "duplicate": "Duplicar", "cancel": "<PERSON><PERSON><PERSON>"}, "deleteProfile": {"title": "Eliminar perfil", "subtitle": "Tem a certeza que pretende eliminar este perfil?", "description": "Esta ação é irreversível e os utilizadores que utilizam este perfil poderão perder o acesso à plataforma."}, "duplicateProfile": {"title": "Duplicar perfil", "subtitle": "Tem a certeza que pretende duplicar este perfil?", "description": "Esta ação irá criar uma cópia deste perfil, incluindo todas as suas permissões e níveis de acesso."}, "new": {"title": "<PERSON><PERSON><PERSON> perfil", "subtitle": "Cria um novo perfil e configura as suas permissões e níveis de acesso.", "cards": {"profileDataTitle": "Informação do perfil", "labelTextArea": "Nome do perfil", "accordionTitle": "Permissões e níveis de acesso"}, "buttons": {"createProfile": "<PERSON><PERSON><PERSON> perfil", "editProfile": "Guardar alterações", "cancel": "<PERSON><PERSON><PERSON>"}}, "edit": {"title": "Administrador", "subtitle": "Configura as permissões e níveis de acesso deste perfil na plataforma."}}, "crm": {"leads": {"title": "Leads", "subtitle": "Gere e acompanha todos os pedidos de informação recebidos.", "label": "Importar Leads", "lead": {"title": "<PERSON><PERSON><PERSON> da <PERSON>", "label": "Ver ficha de contacto", "secondaryLabel": "<PERSON><PERSON><PERSON>", "details": {"assignLeadDialog": {"title": "Reatribuir lead", "alertMessage": "Esta lead contém dados de contacto já existentes no sistema.", "instructionText": "Seleciona o novo utilizador a quem queres atribuir esta lead.", "searchPlaceholder": "Pesquisa utilizador…", "confirmLabel": "Reatribuir lead", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "forms": {"title": "<PERSON><PERSON><PERSON> lead sem sucesso ({id})", "inputLabel": "Indica a razão do fecho e descreve o motivo", "selectReason": "Seleciona a razão", "textFieldPlaceholder": "Insere uma descrição", "buttons": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>"}}, "sections": {"basicInfo": "Informações básicas", "location": "Localização", "assignment": "Atribuição", "course": "Curso", "message": "Mensagem"}, "fields": {"id": "ID", "status": "Estado", "name": "Nome", "email": "Email", "contact": "Contacto", "nif": "NIF", "postalCode": "Código-Postal", "district": "Distrito", "locality": "Localidade", "municipality": "<PERSON><PERSON><PERSON>", "assignedTo": "Atribuído a", "assignmentDate": "Data de atribuição", "creationDate": "Data de criação", "course": "Curso", "assignedCampus": "<PERSON><PERSON><PERSON>", "campus": "<PERSON><PERSON><PERSON>", "educationLevel": "Nível de Escolaridade", "preferredSchedule": "<PERSON><PERSON><PERSON><PERSON>", "message": "Mensagem"}}}, "assignLeadDialog": {"dialogTitle": "Atribuir lead", "alertMessage": "Esta lead contém dados de contacto já existentes no sistema.", "instructionText": "Seleciona o utilizador a quem queres atribuir esta lead.", "searchPlaceholder": "Pesquisa utilizador…", "confirmLabel": "Atribuir lead", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "closeDialog": {"dialogTitle": "<PERSON><PERSON><PERSON> lead com sucesso", "alertMessage": "Esta lead contém dados de contacto já existentes no sistema.", "table": {"columns": {"id": "ID", "nome": "Nome", "email": "Email", "telefone": "Telefone", "telemovel": "Telemóvel", "nif": "NIF", "criadoPor": "<PERSON><PERSON><PERSON> por"}}, "cancelLabel": "<PERSON><PERSON><PERSON>", "confirmWithoutContact": "<PERSON><PERSON><PERSON> s/ contacto", "confirmWithContact": "<PERSON><PERSON><PERSON> c/ contacto"}, "table": {"actions": {"view": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "assign": "Atribuir lead", "closeSuccess": "Fechar c/ sucesso", "closeFail": "<PERSON><PERSON>r s/ sucesso"}, "columns": {"id": "ID", "status": "Estado", "createdAt": "Data criação", "name": "Nome", "email": "Email", "contact": "Contacto", "campus": "Polo atribuição", "course": "Curso", "assignedTo": "Atribuído a"}, "statusOptions": {"new": "Nova", "assigned": "Atribuída", "inQualification": "Em Qualificação", "closedSuccess": "Fechada c/ Sucesso", "closedFail": "Fechada s/ Sucesso", "inValidation": "Em Validação", "cancelled": "Cancelada"}}}, "opportunities": {"title": "Oportunidades", "subtitle": "Gere e acompanha todas as oportunidades em curso.", "label": "Nova oportunidade", "actions": {"view": "<PERSON><PERSON>", "delete": "Eliminar Oportunidade"}, "columns": {"status": "Estado", "createdAt": "Data criação", "name": "Nome", "email": "Email", "contact": "Contacto", "campus": "Polo atribuição", "payment": "Pagamento", "assignedTo": "Atribuído a"}, "dialog": {"confirmDeleteTitle": "Eliminar oportunidade", "confirmDeleteSubtitle": "Tens a certeza de que pretendes eliminar esta oportunidade?", "deleteDescription": "Esta ação é permanente e não poderá ser revertida.", "delete": "Eliminar", "cancel": "<PERSON><PERSON><PERSON>"}, "interactions": {"editItem": "Editar interação", "deleteItem": "Eliminar interação", "editDialog": "Editar interação ({id})", "add": "Adicionar nova interação", "noneExisting": "Não existe nenhuma interação", "edit": {"successMessage": "Oportunidade ({id}) atualizada com sucesso!", "fields": {"type": {"label": "Tipo", "placeholder": "Seleciona tipo"}, "state": {"label": "Estado", "placeholder": "Insere estado..."}, "scheduledDate": {"label": "Data agendada"}, "doneDate": {"label": "Data efetuada"}, "doneBy": {"label": "Efetuada por"}, "createdAt": {"label": "Data criação"}, "text": {"label": "Texto", "placeholder": "Insere texto..."}, "opportunityState": {"label": "Estado da opportunidade", "placeholder": "Seleciona novo estado"}, "closingResult": {"label": "<PERSON>sul<PERSON><PERSON>", "placeholder": "seleciona resultado"}, "closingDate": {"label": "Data Fecho"}, "closingDescription": {"label": "Descrição <PERSON>cho"}}}, "columns": {"description": "Descrição", "type": "Tipo", "state": "Estado", "scheduledDate": "Data agendada", "doneDate": "Data efetuada", "doneBy": "Efetuada por", "enrollment": "<PERSON><PERSON><PERSON><PERSON>"}}, "new": {"title": "Nova ficha de oportunidade", "subtitle": "Preenche os dados abaixo para fechar a lead com sucesso e criar uma nova oportunidade.", "successMessage": "Nova oportunidade criada com sucesso!", "detailsFields": {"description": {"label": "Descrição", "placeholder": "Insere descrição..."}, "contact": {"label": "Contacto", "placeholder": "Insere contacto..."}, "type": {"label": "Tipo", "placeholder": "Seleciona tipo..."}, "entity": {"label": "Entidade", "placeholder": "Pesquisa entidade..."}, "assignedTo": {"label": "Atribuido a"}, "stateDate": {"label": "Data E<PERSON>o"}, "closeDate": {"label": "Data Fecho"}, "resultState": {"label": "<PERSON>sul<PERSON><PERSON>"}, "resultReason": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Insere razão..."}, "resultDescription": {"label": "Descrição fecho", "placeholder": "Insere Descrição"}, "createdBy": {"label": "<PERSON><PERSON><PERSON> por"}, "createdAt": {"label": "Data criação"}, "sourceLead": {"label": "Lead origem"}, "state": {"label": "Estado", "placeholder": "Insere estado..."}, "observations": {"label": "Observações", "placeholder": "Insere observações..."}}, "enrollmentFields": {"course": {"label": "Curso", "placeholder": "Insere curso..."}, "center": {"label": "Polo", "placeholder": "Seleciona polo..."}, "modality": {"label": "Modalidade formação", "placeholder": "Seleciona modalidade"}}}}}, "contacts": {"users": {"title": "<PERSON>cha de contacto", "edit": "<PERSON><PERSON> ", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "newUser": "Novo utilizador"}, "entities": {"title": "Nova ficha de entidades"}}, "users": {"title": "Utilizadores", "subtitle": "Gere e acompanha todos os utilizadores para manter a plataforma sempre atualizada e organizada.", "label": "Novo utilizador", "buttons": {"delete": "Eliminar utilizador", "edit": "Editar utilizador", "assign": "Atribuir polos", "resetPassword": "Redefinir palavra-passe"}, "fields": {"name": {"label": "Nome", "placeholder": "Insere nome..."}, "email": {"label": "Email", "placeholder": "Insere email..."}, "username": {"label": "Username", "placeholder": "Insere o username..."}, "profile": {"label": "Perfil", "admin": "Administrador", "supervisor": "Supervisor Comercial", "placeholder": "Seleciona perfil"}, "status": {"label": "Estado", "active": "Ativo", "inactive": "Inativo"}, "language": {"label": "Idioma", "pt": "Português", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "Seleciona língua"}}, "userForm": {"section": "Novo utilizador", "editUserSubtitle": "Atualiza as informações do utilizador e define os polos onde irá atuar. ", "formLabel": "Informação do utilizador", "assign": {"section": "Polos atribuídos", "notAssigned": "Nenhum polo atribuído", "assigned": "Polo atribuído", "assign": "Atribuir polo"}}, "assignDialog": {"title": "Atribuir polos ao utilizador", "instructionText": "Pesquisar polos onde este utilizador irá atuar.", "searchPlaceholder": "Pesquisar polos...", "confirmLabel": "Atribuir", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "resetPasswordDialog": {"title": "Redefinir palavra-passe ao utlizador", "instructionText": "Será enviado ao utilizador com instruções para redefinir uma nova palavra-passe.", "confirmText": "Tens a certeza que queres redefinir a palavra-passe deste utilizador?", "confirmLabel": "Redefinir", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "deleteDialog": {"title": "Eliminar utilizador", "instructionText": "Esta ação é irreversível e o utilizador perderá o acesso à plataforma.", "confirmText": "Tens a certeza de que pretendes eliminar este utilizador?", "confirmLabel": "Eliminar", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "toast": {"success": {"create": "Utilizador criado com sucesso", "update": "Utilizador atualizado com sucesso", "delete": "Utilizador eliminado com sucesso", "assign": "Polos atribuídos com sucesso", "resetPassword": "Email de redefinição de palavra-passe enviado com sucesso"}, "error": {"create": "Erro ao criar utilizador", "update": "Erro ao atualizar utilizador", "delete": "Erro ao eliminar utilizador", "assign": "Erro ao atribuir polo ao utilizador", "resetPassword": "Erro ao enviar email de redefinição de palavra-passe"}}}}, "gesmat": {"enrollments": {"contract": "Contratualização Geral da Matrícula", "paymentEnrollment": "Pagamento Efetuado no ato da Inscrição", "groups": {"add": "Adicionar matrícula a grupo", "list": "Lista de grupo", "transfer": "Transferir Matrícula", "delete": "Eliminar matr<PERSON><PERSON>", "columns": {"ref": "Ref.", "status": "Estado", "course": "Curso", "center": "Polo", "modality": "Modal. formação", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "expectedStart": "Prev. in<PERSON><PERSON>", "daysSinceOldestEnrollment": "Dias desde matrícula mais antiga", "numTrainees": "Nº formandos", "fundraisingDeadline": "Data limite angariação", "startDate": "Data início", "assignedTo": "Atribuido a", "createdBy": "<PERSON><PERSON><PERSON> por", "createdAt": "Data criação"}}, "fields": {"course": {"label": "Curso", "placeholder": "Insere curso..."}, "courseValue": {"label": "Valor do curso", "placeholder": "Insere valor..."}, "monthlyCount": {"label": "Número men<PERSON>", "placeholder": "Insere número..."}, "hasInsurance": {"label": "Tem seguro próprio"}, "insuranceValue": {"label": "Valor do seguro", "placeholder": "Oferta do seguro"}, "enrollmentValue": {"label": "Valor da inscrição"}, "differentInvoiceContact": {"label": "Dados de faturação diferentes dos dados do formando"}, "recurringPaymentTypes": {"label": "Forma de pagamento recorrente", "options": {"transfer": "Transferência", "reference": "Referência de multibanco", "presential": "Pagamento Presencial"}}, "paymentTypes": {"label": "Forma de pagamento", "options": {"transfer": "Transferência", "card": "Multibanco", "cash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "check": "Cheque", "reference": "Referência de multibanco"}}, "modality": {"label": "Modalidade Formação", "options": {"inPerson": "Presencial", "bLearning": "B-Learning", "eLearning": "E-Learning"}}, "monthlyPaymentValue": {"label": "Valor pago de mensalidade"}, "totalPaid": {"label": "Total pago no ato de inscrição"}, "paymentType": {"label": "Tipo de pagamento"}, "checkNumber": {"label": "Cheque número", "placeholder": "Insere número..."}, "bank": {"label": "Banco", "placeholder": "Insere banco..."}, "agency": {"label": "Agência", "placeholder": "Insere agência..."}}}}, "forms": {"errors": {"name": {"required": "O nome é obrigatório."}, "email": {"invalidFormat": "O email deve ser válido."}, "phone": {"invalidFormat": "O telefone deve ser válido."}, "nif": {"wrongLength": "O NIF deve ser válido."}}, "label": {"cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "edit": "<PERSON><PERSON>"}, "subtab": {"details": "<PERSON><PERSON><PERSON>", "rgpd": "RGPD", "relationships": "Relações", "entities": "Entidades", "attachments": "Anexos", "crmHistory": "CRM-Histórico", "enrollments": "Inscrições", "detailsEnrollment": "<PERSON><PERSON><PERSON> matr<PERSON>cula", "payments": "Faturação", "basicInformation": "Informação Básica", "localization": "Localização", "atribution": "Atribuição", "course": "Curso", "message": "Mensagem", "interactions": "Interações", "invoicing": "Faturação", "groups": "Grupos"}, "opportunityTypes": {"registration": "Registo"}, "interactionTypes": {"call": "Chamada Telefónica", "mail": "Carta", "sms": "SMS"}, "placeholder": "Inserir", "entities": {"label": "Entidades", "placeholder": "Seleciona entidades..."}, "description": {"label": "Descrição", "placeholder": "Insere descrição"}, "name": {"label": "Nome", "placeholder": "Insere nome"}, "email": {"label": "Email", "placeholder": "Insere email"}, "mobile": {"label": "Telemóvel", "placeholder": "Insere telemóvel"}, "phone": {"label": "Telefone", "placeholder": "Insere telefone"}, "nif": {"label": "NIF", "placeholder": "Insere NIF"}, "center": {"label": "Polo", "placeholder": "Seleciona polo..."}, "schedule": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Seleciona horário", "options": {"morning": "Laboral Man<PERSON>ã", "afternoon": "Laboral Tarde", "night": "Pós-Laboral", "none": "N/A", "saturday": "Sábado"}}, "baseEducation": {"label": "Formação base", "options": {"level2": "Nível 2", "level3": "Nível 3", "level2And3": "Níveis 2 e 3"}}, "baseType9": {"label": "Tipo formação base (9º)", "placeholder": "Seleciona modalidade", "options": {"ufcd25": "UFCD 25 horas", "ufcd50": "UFCD 50 horas", "A": "Tipo A (nível 2)", "B": "Tipo B (nível 2)", "C": "Tipo C (nível 2)"}}, "baseType12": {"label": "Tipo formação base (12º)", "placeholder": "Seleciona modalidade", "options": {"A": "Tipo A (nível 3)", "B": "Tipo B (nível 3)", "C": "Tipo C (nível 3)", "portaria": "Portaria 357/2007", "ufcd25": "UFCD 25 horas", "ufcd50": "UFCD 50 horas"}}, "trainee": {"label": "Formando", "placeholder": "Seleciona formando"}, "traineeType": {"label": "<PERSON><PERSON>o formando", "placeholder": "Selecione tipo", "options": {"customer": "Particular", "business": "Empresa"}}, "socialSecurityNumber": {"label": "Nº Segurança Social", "placeholder": "Insere número"}, "identificationType": {"label": "Tipo Doc. Identificação", "placeholder": "Seleciona documento"}, "identificationValidity": {"label": "Validade Doc. Identificação", "placeholder": "MM/DD/YYYY"}, "identificationNumber": {"label": "N° Doc. de Identificação", "placeholder": "Insere número"}, "identificationFile": {"label": "Arquivo Doc. Identificação", "placeholder": "Insere arquivo"}, "address": {"label": "<PERSON><PERSON>", "placeholder": "Insere a morada"}, "postalCode": {"label": "Código Postal", "placeholder": "Insere código postal"}, "locality": {"label": "Localidade", "placeholder": "Insere localidade"}, "district": {"label": "Distrito", "placeholder": "Insere distrito"}, "municipality": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Insere concelho"}, "parish": {"label": "Freguesia", "placeholder": "Insere freguesia"}, "birthPlace": {"label": "Naturalidade", "placeholder": "Insere naturalidade"}, "profession": {"label": "Profissão", "placeholder": "Insere profissão"}, "employerEntity": {"label": "Entidade Empregadora", "placeholder": "Pesquisa entidade"}, "contractType": {"label": "Tipo de Contrato", "placeholder": "Seleciona tipo"}, "birthDate": {"label": "Data de Nascimento", "placeholder": "MM/DD/YYYY"}, "gender": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Seleciona género"}, "educationLevel": {"label": "Escolaridade", "placeholder": "Seleciona escolaridade"}, "iban": {"label": "IBAN", "placeholder": "Insere IBAN"}, "notes": {"label": "Notas", "placeholder": "Insere observações"}, "rgpd": {"consent_1": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei nº 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?", "consent_2": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo período em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este período reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos.", "consent_3": "Autoriza a Entidade Formadora, a fotocopiar o seu documento de identificação para fins de arquivo em Dossier Técnico-Pedagógico?", "consent_4": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?", "consent_5": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"}, "relationships": {"empty": "Não existem relações associadas a este contacto.", "add": "Adicionar <PERSON>", "label": "Nome Contacto", "placeholder": "Pesquisar contacto...", "type": {"label": "Relação", "placeholder": "Seleciona relação"}}, "observations": {"label": "Observações", "placeholder": "Insere observações..."}, "attachments": {"empty": "Não existem ficheiros associados a este contacto.", "label": "<PERSON><PERSON><PERSON><PERSON>", "subLabel": "Ficheiros importados", "modal": {"fileNamePlaceholder": "Insere nome", "filePlaceholder": "<PERSON><PERSON><PERSON>", "fileTypePlaceholder": "Seleciona tipo", "descriptionPlaceholder": "Insere descrição", "tagsPlaceholder": "Insere etiquetas"}}, "crmHistory": {"addInteraction": "Adicionar interação"}, "contacts": {"entities": {"title": "Nova ficha de entidades"}}, "location": {"label": "Localidade", "placeholder": "Insere localidade..."}, "distrito": {"label": "Distrito", "placeholder": "Seleciona distrito..."}, "concelho": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Seleciona concelho"}, "freguesia": {"label": "Freguesia", "placeholder": "Seleciona freguesia"}, "country": {"label": "<PERSON><PERSON>", "placeholder": "PT"}}, "entities": {"details": "<PERSON><PERSON><PERSON>", "protocols": "Protocolos", "fields": {"website": {"label": "Site", "placeholder": "Insere url site..."}, "type": {"label": "Tipo", "placeholder": "Seleciona tipo"}, "cliente_formacao": {"label": "Cliente formação"}, "cliente_faturacao": {"label": "Cliente faturação"}, "entidade_empregadora": {"label": "Entidade empregadora"}, "parceria": {"label": "Parceria"}, "capital_social": {"label": "Capital Social", "placeholder": "Inserir capital social..."}, "autorizar_dados_pessoais": {"label": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei n° 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?"}, "autorizar_processamento_dados": {"label": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo periodo em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este periodo reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos."}, "autorizar_fotocopia": {"label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"}, "autorizar_conteudos_promocionais": {"label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"}, "autorizar_comunicacoes_institucionais": {"label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"}, "praticas": {"label": "Práticas"}, "estagios": {"label": "Est<PERSON><PERSON>s"}}}}