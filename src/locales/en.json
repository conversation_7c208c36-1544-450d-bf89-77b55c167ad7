{"hello": "Hello there!", "welcome": "Welcome to Caixa Mágica", "subtitle": "Building human-oriented software for the public sector, fintechs, and startups.", "gdpr": "GDPR", "success": "Success", "insuccess": "Insuccess", "yes": "Yes", "no": "No", "homepage": {"title": "Your learning journey begins here!", "subtitle": "Digital platform for trainees, trainers and management teams - all in one place."}, "signIn": {"title": "Sign In to Caixa Mágica", "mobileTitle": "Sign In"}, "signUp": {"title": "Do you have an account? Create one", "subtitle": "Create your Caixa Mágica account", "account": "Already have an account? ", "successMessage": "Account created successfully. You can now log in."}, "forgotPassword": {"title": "Forgot your Password?", "instructions": "Check your email to reset your password", "sentTitle": "Sending email!", "sentInstructions": "We have sent you a link to reset your password. Didn't receive it? Check your spam folder or try again in a few minutes.", "resendLink": "Resend link", "backToSignIn": "Back to Sign In", "ResendLinkIn": "Resend email in {time}"}, "resetPassword": {"title": "Reset your password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "successMessage": "Your password was successfully changed. You can now log in."}, "button": {"submit": "Submit", "signIn": "Log In", "signUp": "Sign Up", "cancel": "Cancel", "create": "Create", "SigningUp": "Signing up...", "forgotPassword": "Forgot Password", "sendReset": "Send reset link", "submitting": "Submitting...", "sent": "<PERSON><PERSON>", "createEntity": "Create Entity"}, "nav": {"en": "EN", "pt": "PT"}, "form": {"email": "Email", "password": "Password", "rememberMe": "Remember me"}, "errors": {"invalidCredentials": "Invalid credentials. Please, try again.", "unexpectedError": "An unexpected error occurred. Please, try again later.", "recoverPasswordEmailFailed": "An error occurred while sending the recovery email."}, "dashboard": {"profiles": {"title": "Profiles", "subtitle": "Manage different user profiles and define their access levels.", "label": "New profile", "buttons": {"delete": "Delete", "duplicate": "Duplicate", "cancel": "Cancel"}, "deleteProfile": {"title": "Delete profile", "subtitle": "Are you sure you want to delete this profile?", "description": "This action is irreversible and users with this profile may lose access to the platform."}, "duplicateProfile": {"title": "Duplicate profile", "subtitle": "Are you sure you want to duplicate this profile?", "description": "This will create a copy of this profile, including all its permissions and access levels."}, "new": {"title": "Create profile", "subtitle": "Create a new profile and configure its permissions and access levels.", "cards": {"profileDataTitle": "Profile information", "labelTextArea": "Profile name", "accordionTitle": "Permissions and access levels"}, "buttons": {"createProfile": "Create profile", "editProfile": "Save changes", "cancel": "Cancel"}}, "edit": {"title": "Administrator", "subtitle": "Configure the permissions and access levels of this profile on the platform."}}, "crm": {"leads": {"title": "Leads", "subtitle": "Manage and track all incoming inquiries.", "label": "Import Leads", "lead": {"title": "Lead Details", "label": "View contact profile", "secondaryLabel": "Manage Lead", "details": {"assignLeadDialog": {"title": "Reassign Lead", "alertMessage": "This lead contains contact details that already exist in the system.", "instructionText": "Select the new user to assign this lead to.", "searchPlaceholder": "Search user…", "confirmLabel": "Reassign Lead", "cancelLabel": "Cancel"}, "forms": {"title": "Close lead unsuccessfully ({id})", "inputLabel": "Specify the reason for closing and describe it", "selectReason": "Select reason", "textFieldPlaceholder": "Enter a description", "buttons": {"confirm": "Confirm", "cancel": "Cancel"}}, "sections": {"basicInfo": "Basic Information", "location": "Location", "assignment": "Assignment", "course": "Course", "message": "Message"}, "fields": {"id": "ID", "status": "Status", "name": "Name", "email": "Email", "contact": "Contact", "nif": "NIF", "postalCode": "Postal Code", "district": "District", "locality": "Locality", "municipality": "Municipality", "assignedTo": "Assigned To", "assignmentDate": "Assignment Date", "creationDate": "Creation Date", "course": "Course", "assignedCampus": "Assigned Campus", "campus": "Campus", "educationLevel": "Education Level", "preferredSchedule": "Preferred Schedule", "message": "Message"}}}, "assignLeadDialog": {"dialogTitle": "Assign Lead", "alertMessage": "This lead contains contact data that already exists in the system.", "instructionText": "Select the user to whom you want to assign this lead.", "searchPlaceholder": "Search user…", "confirmLabel": "Assign Lead", "cancelLabel": "Cancel"}, "closeDialog": {"dialogTitle": "Close Lead Successfully", "alertMessage": "This lead contains contact details that already exist in the system.", "table": {"columns": {"id": "ID", "nome": "Name", "email": "Email", "telefone": "Phone", "telemovel": "Mobile", "nif": "NIF", "criadoPor": "Created By"}}, "cancelLabel": "Cancel", "confirmWithoutContact": "Close without Contact", "confirmWithContact": "Close with Contact"}, "table": {"actions": {"view": "View Details", "assign": "Assign Lead", "closeSuccess": "Close with Success", "closeFail": "Close without Success"}, "columns": {"id": "ID", "status": "Status", "createdAt": "Creation Date", "name": "Name", "email": "Email", "contact": "Contact", "campus": "Assigned Campus", "course": "Course", "assignedTo": "Assigned To"}, "statusOptions": {"new": "New", "assigned": "Assigned", "inQualification": "In Qualification", "closedSuccess": "Closed with Success", "closedFail": "Closed without Success", "inValidation": "In Validation", "cancelled": "Cancelled"}}}, "opportunities": {"title": "Opportunities", "subtitle": "Manage and track all ongoing opportunities.", "label": "New opportunity", "actions": {"view": "View Details", "delete": "Delete Opportunity"}, "columns": {"status": "Status", "createdAt": "Creation Date", "name": "Name", "email": "Email", "contact": "Contact", "campus": "Assigned Campus", "payment": "Payment", "assignedTo": "Assigned To"}, "dialog": {"confirmDeleteTitle": "Delete Opportunity", "confirmDeleteSubtitle": "Are you sure you want to delete this opportunity?", "deleteDescription": "This action is permanent and cannot be undone.", "delete": "Delete", "cancel": "Cancel", "columns": {"description": "Description", "type": "Type", "state": "State", "scheduledDate": "Scheduled date", "doneDate": "Done date", "doneBy": "Done by", "enrollment": "Enrollment"}}, "interactions": {"editItem": "Edit interaction", "deleteItem": "Delete interaction", "editDialog": "Edit interaction ({id})", "add": "Adicionar nova interação", "noneExisting": "No interactions yet", "edit": {"successMessage": "Opportunity ({id}) successfully updated!", "fields": {"type": {"label": "Type", "placeholder": "Select type"}, "state": {"label": "State", "placeholder": "Insert state..."}, "scheduledDate": {"label": "Scheduled Date"}, "doneDate": {"label": "Done date"}, "text": {"label": "Text", "placeholder": "Insert text..."}, "opportunityState": {"label": "Opportunity State", "placeholder": "Select new state"}, "closingResult": {"label": "Closing Result", "placeholder": "Select Result"}, "closingDate": {"label": "Closing Date"}, "closingDescription": {"label": "Closing Description"}, "doneBy": {"label": "Done by"}, "createdAt": {"label": "Creation date"}}}}, "new": {"title": "New opportunity form", "subtitle": "Fill the form below to successfully close the lead and create a new opportunity", "sucessMessage": "Successfully created new opportunity!", "detailsFields": {"description": {"label": "Description", "placeholder": "Insert description..."}, "contact": {"label": "Contact", "placeholder": "Insert contact..."}, "type": {"label": "Type", "placeholder": "Select type..."}, "entity": {"label": "Entity", "placeholder": "Search entity..."}, "assignedTo": {"label": "Assigned to"}, "stateDate": {"label": "State Date"}, "closeDate": {"label": "Closing Date"}, "resultState": {"label": "Closing Result"}, "resultReason": {"label": "Closing reason", "placeholder": "Insert reason..."}, "resultDescription": {"label": "Closing Description", "placeholder": "Insert Description"}, "createdBy": {"label": "Created by"}, "createdAt": {"label": "Creation Date"}, "sourceLead": {"label": "Source Lead"}, "state": {"label": "State", "placeholder": "Insert state..."}, "observations": {"label": "Observations", "placeholder": "Insert observations..."}}, "enrollmentFields": {"course": {"label": "Course", "placeholder": "Insert course..."}, "center": {"label": "Center", "placeholder": "Select center..."}, "modality": {"label": "Training modality", "placeholder": "Select modality"}, "schedule": {"label": "Schedule", "placeholder": "Select schedule"}}}}}, "contacts": {"entities": {"title": "New entity form"}}, "users": {"title": "Utilizadores", "subtitle": "Gere e acompanha todos os utilizadores para manter a plataforma sempre atualizada e organizada.", "label": "Novo utilizador", "buttons": {"delete": "Eliminar utilizador", "edit": "Editar utilizador", "assign": "Atribuir polos", "resetPassword": "Redefinir palavra-passe"}, "fields": {"name": {"label": "Nome", "placeholder": "Insere nome..."}, "email": {"label": "Email", "placeholder": "Insere email..."}, "username": {"label": "Username", "placeholder": "Insere o username..."}, "profile": {"label": "Perfil", "admin": "Administrador", "supervisor": "Supervisor Comercial", "placeholder": "Seleciona perfil"}, "status": {"label": "Estado", "active": "Ativo", "inactive": "Inativo"}, "language": {"label": "Idioma", "pt": "Português", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "Seleciona língua"}}, "userForm": {"section": "Novo utilizador", "editUserSubtitle": "Atualiza as informações do utilizador e define os polos onde irá atuar. ", "formLabel": "Informação do utilizador", "assign": {"section": "Polos atribuídos", "notAssigned": "Nenhum polo atribuído", "assigned": "Polo atribuído", "assign": "Atribuir polo"}}, "assignDialog": {"title": "Atribuir polos ao utilizador", "instructionText": "Pesquisar polos onde este utilizador irá atuar.", "searchPlaceholder": "Pesquisar polos...", "confirmLabel": "Atribuir", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "resetPasswordDialog": {"title": "Redefinir palavra-passe ao utlizador", "instructionText": "Será enviado ao utilizador com instruções para redefinir uma nova palavra-passe.", "confirmText": "Tens a certeza que queres redefinir a palavra-passe deste utilizador?", "confirmLabel": "Redefinir", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "deleteDialog": {"title": "Eliminar utilizador", "instructionText": "Esta ação é irreversível e o utilizador perderá o acesso à plataforma.", "confirmText": "Tens a certeza de que pretendes eliminar este utilizador?", "confirmLabel": "Eliminar", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "toast": {"success": {"create": "Utilizador criado com sucesso", "update": "Utilizador atualizado com sucesso", "delete": "Utilizador eliminado com sucesso", "assign": "Polos atribuídos com sucesso", "resetPassword": "Email de redefinição de palavra-passe enviado com sucesso"}, "error": {"create": "Erro ao criar utilizador", "update": "Erro ao atualizar utilizador", "delete": "Erro ao eliminar utilizador", "assign": "Erro ao atribuir polo ao utilizador", "resetPassword": "Erro ao enviar email de redefinição de palavra-passe"}}}, "gesmat": {"enrollments": {"contract": "General Enrolment Contractualisation", "groups": {"add": "Add enrollment to group", "list": "Group list", "transfer": "Transfer enrollment", "delete": "Delete enrollment", "columns": {"ref": "Ref.", "status": "Status", "course": "Course", "center": "Center", "modality": "Training Modality", "schedule": "Schedule", "expectedStart": "Expected Start", "daysSinceOldestEnrollment": "Days since oldest enrollment", "numTrainees": "No. of trainees", "fundraisingDeadline": "Fundraising deadline", "startDate": "Start date", "assignedTo": "Assigned to", "createdBy": "Created by", "createdAt": "Created at"}}, "fields": {"course": {"label": "Course", "placeholder": "Insert course..."}, "courseValue": {"label": "Course value", "placeholder": "Insert value..."}, "monthlyCount": {"label": "Number of monthly payments", "placeholder": "Insert number..."}, "hasInsurance": {"label": "Has own insurance"}, "insuranceValue": {"label": "Insurance Value", "placeholder": "Insurance Offer"}, "enrollmentValue": {"label": "Enrollment Value"}, "differentInvoiceContact": {"label": "Billing data different from trainee data"}, "recurringPaymentTypes": {"label": "Recurring payment type", "options": {"transfer": "Bank Transfer", "reference": "Payment Reference", "presential": "In Person"}}, "paymentTypes": {"label": "Forma de pagamento", "options": {"transfer": "Bank Transfer", "card": "Card", "cash": "Cash", "check": "Check", "reference": "Payment Reference"}}, "modality": {"label": "Learning Modality", "options": {"inPerson": "In Person", "bLearning": "B-Learning", "eLearning": "E-Learning"}}, "monthlyPaymentValue": {"label": "Monthly payment value"}, "totalPaid": {"label": "Total paid upfront"}, "paymentType": {"label": "Tipo de pagamento"}, "checkNumber": {"label": "Check number", "placeholder": "Insert number..."}, "bank": {"label": "Bank", "placeholder": "Insert bank..."}, "agency": {"label": "Agência", "placeholder": "Insert agency..."}}}}}, "forms": {"subtab": {"details": "Details", "rgpd": "GDPR", "relationships": "Relationships", "entities": "Entities", "attachments": "Attachments", "crmhistory": "CRM History", "enrollments": "Enrollments", "detailsEnrollment": "<PERSON><PERSON><PERSON> matr<PERSON>cula", "interactions": "Interactions", "invoicing": "Invoicing", "groups": "Groups"}, "label": {"cancel": "Cancel", "save": "Save", "edit": "Edit"}, "placeholder": "Insert", "description": {"label": "Description", "placeholder": "Insert description"}, "entities": {"label": "Entities", "placeholder": "Select entities..."}, "opportunityTypes": {"registration": "Registration"}, "interactionTypes": {"call": "Phone Call", "mail": "Letter", "sms": "SMS"}, "name": {"label": "Name", "placeholder": "Enter name"}, "email": {"label": "Email", "placeholder": "Enter email"}, "mobile": {"label": "Mobile", "placeholder": "Enter mobile"}, "phone": {"label": "Phone", "placeholder": "Enter phone"}, "nif": {"label": "NIF", "placeholder": "Enter NIF"}, "center": {"label": "Center", "placeholder": "Select center..."}, "schedule": {"label": "Schedule", "placeholder": "Select schedule", "options": {"morning": "Morning", "afternoon": "Afternoon", "night": "Night", "none": "N/A", "saturday": "Saturday"}}, "baseEducation": {"label": "Base Education", "options": {"level2": "Level 2", "level3": "Level 3", "level2And3": "Levels 2 and 3"}}, "baseType9": {"label": "Base education type (9º)", "placeholder": "Seleciona modalidade", "options": {"ufcd25": "UFCD 25 hours", "ufcd50": "UFCD 50 hours", "A": "Type A (level 2)", "B": "Type B (level 2)", "C": "Type C (level 2)"}}, "baseType12": {"label": "Base education type (12º)", "placeholder": "Seleciona modalidade", "options": {"A": "Type A (level 3)", "B": "Type B (level 3)", "C": "Type C (level 3)", "portaria": "Portaria 357/2007", "ufcd25": "UFCD 25 hours", "ufcd50": "UFCD 50 hours"}}, "trainee": {"label": "Trainee", "placeholder": "Select trainee"}, "traineeType": {"label": "Trainee type", "placeholder": "Select type", "options": {"customer": "Individual", "business": "Business"}}, "socialSecurityNumber": {"label": "Social Security Number", "placeholder": "Enter number"}, "identificationType": {"label": "ID Document Type", "placeholder": "Select document"}, "identificationValidity": {"label": "ID Document Validity", "placeholder": "MM/DD/YYYY"}, "identificationNumber": {"label": "ID Document Number", "placeholder": "Enter number"}, "identificationFile": {"label": "ID Document File", "placeholder": "Enter file"}, "address": {"label": "Address", "placeholder": "Enter address"}, "postalCode": {"label": "Postal Code", "placeholder": "Enter postal code"}, "locality": {"label": "Locality", "placeholder": "Enter locality"}, "district": {"label": "District", "placeholder": "Enter district"}, "municipality": {"label": "Municipality", "placeholder": "Enter municipality"}, "parish": {"label": "Parish", "placeholder": "Enter parish"}, "birthPlace": {"label": "Birthplace", "placeholder": "Enter birthplace"}, "profession": {"label": "Profession", "placeholder": "Enter profession"}, "employerEntity": {"label": "Employer Entity", "placeholder": "Search entity"}, "contractType": {"label": "Contract Type", "placeholder": "Select type"}, "birthDate": {"label": "Birth Date", "placeholder": "MM/DD/YYYY"}, "gender": {"label": "Gender", "placeholder": "Select gender"}, "educationLevel": {"label": "Education Level", "placeholder": "Select education level"}, "iban": {"label": "IBAN", "placeholder": "Enter IBAN"}, "notes": {"label": "Notes", "placeholder": "Enter notes"}, "observations": {"label": "Observations", "placeholder": "Insert observations..."}, "rgpd": {"consent_1": "Do you authorize the use of my personal data, contained in this form, under Law No. 67/98, of October 26, for the purpose of computerized processing of processes and homologation/certification, statistical assessment, and monitoring of the training carried out by the certifying entity, namely the Directorate-General for Employment (DGERT)?", "consent_2": "Do you authorize the Training Entity to process the data and keep it for the period during which the process is open for administrative, evaluation, and audit purposes? During this period, the data subject reserves the right to access and rectify the data.", "consent_3": "Do you authorize the Training Entity to photocopy your identification document for filing in the Technical-Pedagogical Dossier?", "consent_4": "Do you authorize the data contained in this form to be used by the Training Entity for sending commercial and/or promotional content?", "consent_5": "Do you authorize the data contained in this form to be used by the Training Entity for sending institutional and informational communications?"}, "relationships": {"empty": "There are no relationships associated with this contact.", "add": "Add relationship", "label": "Contact Name", "placeholder": "Search contact...", "type": {"label": "Relationship", "placeholder": "Select relationship"}}, "attachments": {"empty": "There are no files associated with this contact.", "label": "Add file", "subLabel": "Imported files", "modal": {"fileNamePlaceholder": "Enter name", "filePlaceholder": "File", "fileTypePlaceholder": "Select type", "descriptionPlaceholder": "Enter description", "tagsPlaceholder": "Enter tags"}}, "crmHistory": {"addInteraction": "Add interaction"}, "contacts": {"entities": {"title": "New entity record"}}, "location": {"label": "Location", "placeholder": "Enter location..."}, "distrito": {"label": "District", "placeholder": "Select district..."}, "concelho": {"label": "County", "placeholder": "Select county"}, "freguesia": {"label": "Parish", "placeholder": "Select parish"}, "country": {"label": "Country", "placeholder": "PT"}}, "entities": {"details": "Details", "protocols": "Protocols", "fields": {"website": {"label": "Website", "placeholder": "Enter website url..."}, "type": {"label": "Type", "placeholder": "Select type"}, "cliente_formacao": {"label": "Training client"}, "cliente_faturacao": {"label": "Billing client"}, "entidade_empregadora": {"label": "Employer entity"}, "parceria": {"label": "Partnership"}, "capital_social": {"label": "Share Capital", "placeholder": "Insert share capital..."}, "autorizar_dados_pessoais": {"label": "Do you authorize the use of my personal data, contained in this form, under the terms of Law No. 67/98, of October 26, for the purpose of computer processing of processes and homologation/certification, statistical calculation and monitoring of the training carried out by the certifying entity, namely the Directorate General for Employment (DGERT)?"}, "autorizar_processamento_dados": {"label": "Do you authorize the Training Entity to carry out the computer processing of the data and to keep them for the period in which the process is open for administrative, evaluative and audit purposes? During this period, the data subject reserves the right to access and rectify them."}, "autorizar_fotocopia": {"label": "Do you authorize the data contained in this form to be used by the Training Entity to send commercial and/or promotional content?"}, "autorizar_conteudos_promocionais": {"label": "Do you authorize the data contained in this form to be used by the Training Entity to send commercial and/or promotional content?"}, "autorizar_comunicacoes_institucionais": {"label": "Do you authorize the data contained in this form to be used by the Training Entity to send institutional and informative communications?"}, "praticas": {"label": "Practices"}, "estagios": {"label": "Internships"}}}}