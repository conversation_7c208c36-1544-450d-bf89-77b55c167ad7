'use client';

import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';

const fieldConfig: FieldConfig = {
  name: 'observations',
  label: 'forms.observations.label',
  placeholder: 'forms.observations.placeholder',
  type: 'text',
  multiline: true,
  colSpan: 12,
};

export const ObservationsField = (props: Partial<ComposableFormFieldProps>) => (
  <ComposableForm.Field {...fieldConfig} {...props} />
);
