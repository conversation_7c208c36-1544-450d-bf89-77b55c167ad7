'use client';

import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';

const fieldConfig: FieldConfig = {
  name: 'name',
  label: 'forms.name.label',
  placeholder: 'forms.name.placeholder',
  type: 'text',
};

export const NameField = (props: Partial<ComposableFormFieldProps>) => (
  <ComposableForm.Field {...fieldConfig} {...props} />
);
