import { useMemo } from 'react';
import { useContactsQuery } from '../../../lib/queries/contacts';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldOption } from '../types/formBuilder';
import { Search } from '@mui/icons-material';

export function ProfileSelectField(props: Omit<ComposableFormFieldProps, 'options' | 'type'>) {
  const { contacts, loading } = useContactsQuery();

  const options = useMemo(
    () =>
      contacts.map<FieldOption>((c) => ({
        label: c.name || '-',
        value: c.id,
      })),
    [contacts]
  );

  return (
    <ComposableForm.Field
      icon={<Search />}
      type={'select'}
      label={'forms.trainee.label'}
      placeholder={'forms.trainee.placeholder'}
      loading={loading}
      options={options}
      {...props}
    />
  );
}
