import { useOptionsIntl } from '../../../hooks/useFieldOptionsIntl';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';

const useShiftOptions = () =>
  useOptionsIntl(['morning', 'afternoon', 'night', 'none', 'saturday'], 'forms.schedule.options');

export const ShiftField = ({
  name = 'shift',
  label = 'forms.schedule.label',
  placeholder = 'forms.schedule.placeholder',
  type = 'select',
  ...rest
}: Omit<Partial<ComposableFormFieldProps>, 'options'>) => {
  const options = useShiftOptions();

  return (
    <ComposableForm.Field
      name={name}
      label={label}
      placeholder={placeholder}
      type={type}
      options={options}
      {...rest}
    />
  );
};
