'use client';

import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';

const fieldConfig: FieldConfig = {
  name: 'email',
  label: 'forms.email.label',
  placeholder: 'forms.email.placeholder',
  type: 'text',
};

export const EmailField = (props: Partial<ComposableFormFieldProps>) => (
  <ComposableForm.Field {...fieldConfig} {...props} />
);
