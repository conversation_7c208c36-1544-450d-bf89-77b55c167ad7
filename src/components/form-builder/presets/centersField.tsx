import { useMemo } from 'react';
import { useCentersQuery } from '../../../lib/queries/centers';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { mapLabel } from '../../../utils';

type FieldProps = Omit<ComposableFormFieldProps, 'type'>;

export const CentersSelectField = (props: FieldProps) => {
  const { centers, loading } = useCentersQuery();

  const options = useMemo(() => mapLabel(centers), [centers]);

  return (
    <ComposableForm.Field
      label={'forms.center.label'}
      placeholder={'forms.center.placeholder'}
      type={'select'}
      options={options}
      loading={loading}
      {...props}
    />
  );
};
