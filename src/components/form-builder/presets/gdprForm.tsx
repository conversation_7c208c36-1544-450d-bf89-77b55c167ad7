import { rgpdFields } from '../formConfigs/userConfig';
import { ComposableForm } from '../ComposableFormBuilder';

type GDPRConsent = `consent_${1 | 2 | 3 | 4 | 5}`;

export function GDPRForm({
  requiredConsents = [],
  readonly,
}: {
  requiredConsents?: GDPRConsent[];
  readonly?: boolean;
}) {
  return (
    <>
      {rgpdFields({ readonly }).map((f) => (
        <ComposableForm.Field
          key={f.name}
          required={requiredConsents.some((c) => c === `rgpd.${f.name}`)}
          {...f}
        />
      ))}
    </>
  );
}