'use client';

import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';

const fieldConfig: FieldConfig = {
  name: 'nif',
  label: 'forms.nif.label',
  placeholder: 'forms.nif.placeholder',
  type: 'text',
};

export const NifField = (props: Partial<ComposableFormFieldProps>) => (
  <ComposableForm.Field {...fieldConfig} {...props} />
);
