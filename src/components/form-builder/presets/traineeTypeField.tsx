import { useOptionsIntl } from '../../../hooks/useFieldOptionsIntl';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';

const useOptions = () => useOptionsIntl(['customer', 'business'], 'forms.traineeType.options');

export function TraineeTypeField(
  props: Partial<Omit<ComposableFormFieldProps, 'options' | 'type'>>
) {
  const options = useOptions();

  return (
    <ComposableForm.Field
      name={'traineeType'}
      label={'forms.traineeType.label'}
      placeholder="forms.traineeType.placeholder"
      type={'select'}
      options={options}
      {...props}
    />
  );
}
