import { Search } from '@mui/icons-material';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { useEntitiesQuery } from '../../../lib/queries/entities';
import { useMemo } from 'react';
import { FieldOption } from '../types/formBuilder';

export const EntitySelectField = (props: Omit<ComposableFormFieldProps, 'type' | 'options'>) => {
  const { entities, loading } = useEntitiesQuery();

  const options = useMemo(
    () =>
      entities.map<FieldOption>((e) => ({
        label: e.name || ' - ',
        value: e.id || ' - ',
      })),
    [entities]
  );

  return (
    <ComposableForm.Field
      icon={<Search />}
      label={'forms.entities.label'}
      placeholder={'forms.entities.placeholder'}
      options={options}
      loading={loading}
      type={'select'}
      {...props}
    />
  );
};
