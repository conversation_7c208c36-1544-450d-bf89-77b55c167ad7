'use client';

import React, { useState } from 'react';
import {
  Box,
  TextField,
  List,
  ListItem,
  Checkbox,
  ListItemText,
  ListItemButton,
  InputAdornment,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { Controller } from 'react-hook-form';

export interface SelectableItem {
  id: string;
  label: string;
}

interface SearchableCheckboxListProps {
  items: SelectableItem[];
  placeholder: string;
  name: string;
  control: any;
}

export default function SearchableCheckboxList({
  items,
  placeholder,
  name,
  control,
}: SearchableCheckboxListProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredItems = items.filter((item) =>
    item.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={[]}
      render={({ field }) => {
        const selectedValues: string[] = field.value ?? [];
        const handleToggle = (id: string) => {
          const newValues = selectedValues.includes(id)
            ? selectedValues.filter((val) => val !== id)
            : [...selectedValues, id];
          field.onChange(newValues);
        };

        return (
          <Box sx={{ width: '100%' }}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                },
              }}
              sx={{ mb: 1, mt: 1 }}
            />
            <List dense sx={{ height: 180, minHeight: 160 }}>
              {filteredItems.map((item) => (
                <ListItem
                  key={item.id}
                  disablePadding
                  secondaryAction={
                    <Checkbox
                      edge="end"
                      checked={field.value.includes(item.id)}
                      onChange={() => handleToggle(item.id)}
                    />
                  }
                  sx={{ minHeight: 42 }}
                >
                  <ListItemButton onClick={() => handleToggle(item.id)}>
                    <ListItemText primary={item.label} />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Box>
        );
      }}
    />
  );
}
