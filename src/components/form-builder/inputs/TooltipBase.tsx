'use client';

import { Tooltip, Box } from '@mui/material';
import { useTranslations } from 'next-intl';

interface BaseTooltipProps {
  label: string;
  tooltip?: string;
  children?: React.ReactElement;
}

const BaseTooltip: React.FC<BaseTooltipProps & { shouldTranslate?: boolean }> = ({
  label,
  tooltip,
  children,
  shouldTranslate = false,
}) => {
  const t = useTranslations();
  const title = shouldTranslate ? t(tooltip ?? label) : (tooltip ?? label);

  return (
    <Tooltip followCursor title={title}>
      {children ?? (
        <Box
          component="p"
          sx={{
            display: 'block',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '100%',
          }}
        >
          {shouldTranslate ? t(label) : label}
        </Box>
      )}
    </Tooltip>
  );
};

export default BaseTooltip;