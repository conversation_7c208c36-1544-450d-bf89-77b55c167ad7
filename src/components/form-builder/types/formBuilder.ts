import { createContext, useContext, ReactNode } from 'react';
import { SectionMock } from '../../../lib/mocks/permissionSections.mock';
import { SelectableItem } from '../inputs/SearchableCheckboxList';

export interface FieldCondition {
  field: string;
  equals: unknown;
}

export interface FieldOption {
  label: string;
  value: string | number | boolean;
  tooltip?: string;
}

// needs to be organized by type, so properties become more readable
export type FieldType =
  | 'text'
  | 'readonly'
  | 'number'
  | 'select'
  | 'label'
  | 'empty'
  | 'fileUploadModal'
  | 'TextInputFileUpload'
  | 'date'
  | 'datetime'
  | 'time'
  | 'checkbox'
  | 'multiFieldLine'
  | 'iconButton'
  | 'iconButtonText'
  | 'fileGroup'
  | 'fileUpload'
  | 'phonenumber'
  | 'permissionAccordion'
  | 'togglebutton'
  | 'SearchableCheckboxList';

export interface FieldConfig {
  name: string;
  label?: string;
  labelValue?: string;
  loading?: boolean;
  tooltip?: string;
  files?: { id: string; published: boolean }[]; // used in fileUploadModal
  modalLabel?: string;
  id?: string;
  subLabel?: string;
  parent?: string;
  required?: boolean;
  endAdornment?: ReactNode;
  readonly?: boolean;
  disabled?: boolean;
  placeholder?: string;
  helperText?: string;
  type: FieldType;
  options?: FieldOption[];
  colSpan?: number; // 1-12 (12 = width total)
  fullWidth?: boolean; // força 12 colunas
  condition?: FieldCondition;
  subFields?: FieldConfig[];
  multiline?: boolean;
  multiple?: boolean; // used in select
  actionPrimary?: () => void;
  actionSecondary?: () => void;
  actionPrimaryLabel?: string;
  actionSecondaryLabel?: string;
  actionPrimaryDisabled?: boolean;
  actionSecondaryDisabled?: boolean;
  actionPrimaryIcon?: React.ElementType;
  actionSecondaryIcon?: React.ElementType;
  action?: () => void;
  icon?: ReactNode;
  actionLabel?: string;
  isTranslation?: boolean;
  screenshotId?: string;
  disableClearable?: boolean;
  labelContainer?: string;
  permissionSections?: SectionMock[];
  resultsOnlyWithSearch?: boolean; // Only show results after searching
  createAction?: () => string;
  removeAction?: (id: string) => void;
  submitAction?: (args: { id: string }) => void;
  cancelAction?: () => void;
  deleteAction?: () => void;
  downloadAction?: () => void;
  modalConfig?: (id: string) => FieldConfig[];
  items?: SelectableItem[];
  value?: [];
}

export interface FormBuilderProps {
  label?: string;
  fields: FieldConfig[];
  columns?: 1 | 2 | 3 | 4; // só para restringir
}

export type FormBuilderContextType = {
  columns: number;
};

export const FormBuilderContext = createContext<FormBuilderContextType>({
  columns: 1,
});

export const useFormBuilderContext = () => useContext(FormBuilderContext);
