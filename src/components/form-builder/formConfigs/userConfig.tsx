import { FieldConfig } from '../types/formBuilder';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import { NameField } from '../presets/nameField';
import { EmailField } from '../presets/emailField';
import { ComposableForm } from '../ComposableFormBuilder';
import { PhoneField } from '../presets/phoneField';
import { NifField } from '../presets/nifField';
import React from 'react';

type DetailsFieldsArgs = {
  readonly?: boolean;
  options?: Record<string, any[]>;
  employer?: string[];
  action?: {
    employer?: {
      edit: () => void;
      create: () => void;
    };
  };
};
type ContactUserDetailsFieldsProps = {
  readonly?: boolean;
  selectOptions?: Record<string, any>;
};

export const rgpdFields = (
  { readonly }: DetailsFieldsArgs = { readonly: false }
): FieldConfig[] => [
  {
    name: 'rgpd.consent_1',
    label: 'forms.rgpd.consent_1',
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_2',
    label: 'forms.rgpd.consent_2',
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_3',
    label: 'forms.rgpd.consent_3',
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_4',
    label: 'forms.rgpd.consent_4',
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_5',
    label: 'forms.rgpd.consent_5',
    type: 'checkbox',
    readonly,
  },
];

export const ContactUserDetailsFields: React.FC<ContactUserDetailsFieldsProps> = ({
  readonly = false,
  selectOptions = {},
}) => (
  <>
    <NameField readonly={readonly} />
    <EmailField readonly={readonly} />
    <ComposableForm.Field
      name="mobile"
      label="forms.mobile.label"
      placeholder="forms.mobile.placeholder"
      type="phonenumber"
      readonly={readonly}
    />
    <PhoneField readonly={readonly} />
    <NifField readonly={readonly} />
    <ComposableForm.Field
      name="socialSecurityNumber"
      label="forms.socialSecurityNumber.label"
      placeholder="forms.socialSecurityNumber.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="identificationType"
      label="forms.identificationType.label"
      placeholder="forms.identificationType.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="identificationValidity"
      label="forms.identificationValidity.label"
      placeholder="forms.identificationValidity.placeholder"
      type="date"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="identificationNumber"
      label="forms.identificationNumber.label"
      placeholder="forms.identificationNumber.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="identificationFile"
      label="forms.identificationFile.label"
      placeholder="forms.identificationFile.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="address"
      label="forms.address.label"
      placeholder="forms.address.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="postalCode"
      label="forms.postalCode.label"
      placeholder="forms.postalCode.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="locality"
      label="forms.locality.label"
      placeholder="forms.locality.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="district"
      label="forms.district.label"
      placeholder="forms.district.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.district}
    />
    <ComposableForm.Field
      name="municipality"
      label="forms.municipality.label"
      placeholder="forms.municipality.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.municipality}
    />
    <ComposableForm.Field
      name="parish"
      label="forms.parish.label"
      placeholder="forms.parish.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.parish}
    />
    <ComposableForm.Field
      name="country"
      label="forms.country.label"
      placeholder="forms.country.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.country}
    />
    <ComposableForm.Field
      name="birthPlace"
      label="forms.birthPlace.label"
      placeholder="forms.birthPlace.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="profession"
      label="forms.profession.label"
      placeholder="forms.profession.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="employerEntity"
      type="select"
      options={selectOptions?.employer ?? []}
      resultsOnlyWithSearch={true}
      readonly={readonly}
      placeholder="forms.employerEntity.placeholder"
      label="forms.employerEntity.label"
      actionPrimary={() => console.log('Edit employer entity')}
      actionPrimaryIcon={EditIcon}
      actionPrimaryLabel="forms.employerEntity.edit"
      actionPrimaryDisabled={true}
      actionSecondary={() => console.log('Add employer entity')}
      actionSecondaryIcon={AddCircleIcon}
      actionSecondaryLabel="forms.employerEntity.add"
    />
    <ComposableForm.Field
      name="contractType"
      label="forms.contractType.label"
      placeholder="forms.contractType.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.contractType}
    />
    <ComposableForm.Field
      name="birthDate"
      label="forms.birthDate.label"
      placeholder="forms.birthDate.placeholder"
      type="date"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="gender"
      label="forms.gender.label"
      placeholder="forms.gender.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.gender}
    />
    <ComposableForm.Field
      name="educationLevel"
      label="forms.educationLevel.label"
      placeholder="forms.educationLevel.placeholder"
      type="select"
      readonly={readonly}
      options={selectOptions?.educationLevel}
    />
    <ComposableForm.Field
      name="iban"
      label="forms.iban.label"
      placeholder="forms.iban.placeholder"
      type="text"
      readonly={readonly}
    />
    <ComposableForm.Field
      name="notes"
      label="forms.notes.label"
      placeholder="forms.notes.placeholder"
      type="text"
      colSpan={12}
      readonly={readonly}
      multiline={true}
    />
  </>
);

type SequenceFieldsProps = {
  formOptions: any;
  selectOptions: any;
  readonly: boolean;
  setFormOptions: React.Dispatch<React.SetStateAction<any>>;
  fieldKey: string;
};
export const SequenceFields: React.FC<SequenceFieldsProps> = ({
  formOptions,
  selectOptions,
  readonly,
  setFormOptions,
  fieldKey,
}) => (
  <>
    <ComposableForm.Field colSpan={12} name={fieldKey} type="multiFieldLine">
      {formOptions[`${fieldKey}Types`].map((key: string) => (
        <React.Fragment key={key}>
          <ComposableForm.Field
            key={key}
            name={`${fieldKey}.${key}.user`}
            label={`forms.${fieldKey}.label`}
            placeholder={`forms.${fieldKey}.placeholder`}
            options={selectOptions?.users ?? []}
            type="select"
            resultsOnlyWithSearch={true}
            colSpan={6}
            readonly={readonly}
          />
          <ComposableForm.Field
            key={`${key}-type`}
            name={`${fieldKey}.${key}.relationshipType`}
            label={`forms.${fieldKey}.type.label`}
            placeholder={`forms.${fieldKey}.type.placeholder`}
            type="select"
            options={selectOptions?.[`${fieldKey}Types`] ?? []}
            colSpan={6}
            readonly={readonly}
            actionPrimary={() =>
              setFormOptions((prev: any) => ({
                ...prev,
                [`${fieldKey}Types`]: prev[`${fieldKey}Types`].filter(
                  (type: string) => type !== key
                ),
              }))
            }
            actionPrimaryIcon={DeleteIcon}
            actionPrimaryLabel={`forms.${fieldKey}.delete`}
          />
        </React.Fragment>
      ))}
    </ComposableForm.Field>
    <ComposableForm.Field
      name={`${fieldKey}.addAction`}
      actionLabel={`forms.${fieldKey}.add`}
      type="iconButtonText"
      colSpan={12}
      disabled={false}
      action={() =>
        setFormOptions((prev: any) => ({
          ...prev,
          [`${fieldKey}Types`]: [...prev[`${fieldKey}Types`], `new-${Date.now()}`],
        }))
      }
      icon={<AddIcon />}
      readonly={readonly}
    />
  </>
);
type AttachmentsFieldsProps = {
  readonly: boolean;
  formOptions: any;
  setFormOptions: React.Dispatch<React.SetStateAction<any>>;
  selectOptions: any;
  methods: any;
};

export const AttachmentsFields: React.FC<AttachmentsFieldsProps> = ({
  readonly,
  formOptions,
  setFormOptions,
  selectOptions,
  methods,
}) => (
  <>
    <ComposableForm.Field name="attachments.empty1" type="empty" colSpan={3} />
    <ComposableForm.Field
      name="attachments"
      label="forms.attachments.label"
      type="fileUploadModal"
      readonly={readonly}
      files={formOptions?.files ?? []}
      modalLabel="forms.attachments.modal.label"
      createAction={() => {
        const id = `new-${Date.now()}`;
        setFormOptions((prev: any) => ({
          ...prev,
          files: [...(prev.files ?? []), { id, published: false }],
        }));
        return id;
      }}
      cancelAction={() => {
        let id;
        setFormOptions((prev: any) => {
          const unpublished = prev.files?.find((file: any) => !file.published);
          if (unpublished) {
            id = unpublished.id;
          }
          return {
            ...prev,
            files: (prev.files ?? []).filter((file: any) => file.published),
          };
        });
        // we could use usefieldarray, but objects are easier to keep track
        methods.unregister(`attachments.${id}`);
      }}
      submitAction={({ id }: { id: string }) => {
        setFormOptions((prev: any) => ({
          ...prev,
          files: (prev.files ?? []).map((file: any) =>
            file.id === id ? { ...file, published: true } : file
          ),
        }));
      }}
      colSpan={6}
      subLabel="forms.attachments.subLabel"
      options={selectOptions?.fileTypes ?? []}
      modalConfig={(id: string) => [
        {
          name: `${id}.fileName`,
          type: 'text',
          placeholder: 'forms.attachments.modal.fileNamePlaceholder',
          readonly,
          colSpan: 9,
        },
        {
          name: `${id}.file`,
          type: 'fileUpload',
          placeholder: 'forms.attachments.modal.filePlaceholder',
          readonly,
          colSpan: 3,
        },
        {
          name: `${id}.fileType`,
          type: 'select',
          options: selectOptions?.fileTypes ?? [],
          placeholder: 'forms.attachments.modal.fileTypePlaceholder',
          readonly,
          colSpan: 12,
        },
        {
          name: `${id}.description`,
          type: 'text',
          placeholder: 'forms.attachments.modal.descriptionPlaceholder',
          multiline: true,
          colSpan: 12,
          readonly,
        },
        {
          name: `${id}.tags`,
          type: 'text',
          placeholder: 'forms.attachments.modal.tagsPlaceholder',
          colSpan: 12,
          readonly,
        },
      ]}
    >
      {formOptions?.files
        ?.filter((file: { published: boolean }) => file.published)
        ?.map(({ id }: { id: string }) => (
          <ComposableForm.Field
            key={id}
            name={`attachments.${id}.fileGroup`}
            id={id}
            parent={'attachments'}
            label={'forms.attachments.type'}
            type="fileGroup"
            deleteAction={() => {
              setFormOptions((prev: any) => ({
                ...prev,
                files: (prev.files ?? []).filter((file: any) => file.id !== id),
              }));
              methods.unregister(`attachments.${id}`);
            }}
            downloadAction={() => {
              // No operation for now
            }}
          />
        ))}
    </ComposableForm.Field>
    <ComposableForm.Field name="attachments.empty2" type="empty" colSpan={3} />
  </>
);
