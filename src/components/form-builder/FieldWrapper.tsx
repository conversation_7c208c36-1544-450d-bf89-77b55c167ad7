'use client';

import { Box, Grid, Skeleton } from '@mui/material';

import { FieldConfig, useFormBuilderContext } from './types/formBuilder';
import FormField, { CustomLabel } from './FormField';
import { ReactNode } from 'react';

const FieldWrapper = ({ field, children }: { field: FieldConfig; children?: ReactNode }) => {
  const { columns } = useFormBuilderContext();

  // Distribuição automática de colunas
  const gridSize = field.fullWidth ? 12 : (field.colSpan ?? Math.round(12 / columns));

  return (
    <Grid key={field.name} size={gridSize}>
      {field.loading ? (
        <>
          <CustomLabel label={field.label || ''} required={field.required} />
          <Skeleton
            variant="rectangular"
            width="100%"
            height={'40px'}
            sx={{
              borderRadius: '8px',
            }}
          />
          {/* helper text height */}
          <Box sx={{ minHeight: '24px' }} />
        </>
      ) : (
        <FormField field={field}>{children}</FormField>
      )}
    </Grid>
  );
};

export default FieldWrapper;
