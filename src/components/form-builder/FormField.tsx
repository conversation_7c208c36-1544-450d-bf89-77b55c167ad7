'use client';

import {
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  TextField,
  ToggleButtonGroup,
  ToggleButton,
  Typography,
} from '@mui/material';
import { Controller, FieldError, useFormContext, UseFormReturn } from 'react-hook-form';

import { FieldConfig, FieldOption } from './types/formBuilder';

import SelectField from './inputs/SelectField';
import { useTranslations } from 'next-intl';
import PermissionAccordionField from './inputs/PermissionAccordionField';
import FileCard from './inputs/FileCard';
import FileUploadModal from './inputs/FileUploadModal';
import FileUploadField from './inputs/FileUploadField';
import IconButtonText from './inputs/IconButtonText';
import { DatePicker, DateTimePicker, TimePicker } from '@mui/x-date-pickers';
import { ReactNode, useCallback } from 'react';
import { ComposableForm } from './ComposableFormBuilder';
import SearchableCheckboxList from './inputs/SearchableCheckboxList';

//maybe not needed now, but if there are problems while fetching errors on fields, this function could help
/*function getErrorMessage(errors: FieldErrors, path: string): string | undefined {
  const errorObj = path
    .split('.')
    .reduce<unknown>(
      (obj, key) =>
        obj && typeof obj === 'object' ? (obj as Record<string, unknown>)[key] : undefined,
      errors
    );
  return errorObj && typeof errorObj === 'object' && 'message' in errorObj
    ? (errorObj as { message?: string }).message
    : undefined;
}*/

export interface FormFieldProps {
  field: FieldConfig;
  children?: ReactNode;
  error?: FieldError;
}

/** ---- etiqueta reutilizável ---- */
export function CustomLabel({
  label,
  required = false,
  disabled = false,
}: {
  label: string;
  required?: boolean;
  disabled?: boolean;
}) {
  const t = useTranslations();

  return label ? (
    <Typography
      component={'label'}
      sx={(theme) => ({
        fontWeight: 500,
        display: 'block',
        color: disabled ? theme.palette.action.disabled : undefined,
        '&::after': required
          ? {
              content: '"*"',
              color: disabled ? theme.palette.action.disabled : theme.palette.error.main,
              marginLeft: '0.2em',
            }
          : undefined,
      })}
    >
      {t(label)}
    </Typography>
  ) : null;
}

const FormField = ({ field, children }: FormFieldProps) => {
  const t = useTranslations();
  const methods = useFormContext();
  //const error = methods.formState.errors[field.name];

  const labelComponent = useCallback(
    (label?: string) => (
      <CustomLabel
        label={label ?? field.label ?? field.subLabel ?? ''}
        required={field.required}
        disabled={field.disabled}
      />
    ),
    [field.label, field.subLabel, field.required, field.disabled]
  );

  const placeholder = field.placeholder ? t(field.placeholder) : t('forms.placeholder');
  /** ---- renderização por tipo ---- */
  switch (field.type) {
    /** TEXT ------------------------------------------------------------- */
    case 'text':
      return (
        <>
          {labelComponent()}
          <Controller
            name={field.name}
            control={methods.control}
            rules={{
              required: field.required,
            }}
            render={({ field: f, fieldState }) => (
              <TextField
                {...f}
                value={f.value ?? ''}
                placeholder={placeholder}
                disabled={field.readonly || field.disabled}
                fullWidth
                multiline={field.multiline}
                minRows={field.multiline ? 4 : undefined}
                error={!!fieldState.error}
                helperText={
                  fieldState.error?.message ? t(String(fieldState.error.message)) : '\u00A0'
                }
              />
            )}
          />
        </>
      );

    /** READ-ONLY -------------------------------------------------------- */
    case 'readonly':
      return (
        <>
          {labelComponent()}
          <TextField
            slotProps={{ input: { readOnly: true } }}
            id={field.name}
            defaultValue={field.placeholder}
            placeholder={placeholder}
            {...methods.register(field.name)}
          />
        </>
      );

    case 'permissionAccordion':
      return (
        <PermissionAccordionField
          permissionSections={field.permissionSections ?? []}
          disabled={field.disabled}
        />
      );

    case 'number': {
      return (
        <>
          {labelComponent()}
          <Controller
            name={field.name}
            defaultValue={0}
            control={methods.control}
            rules={{
              required: field.required,
            }}
            render={({ field: f, fieldState }) => {
              return (
                <TextField
                  {...f}
                  id={field.name}
                  type={'number'}
                  placeholder={placeholder}
                  disabled={field.readonly}
                  slotProps={{
                    input: {
                      endAdornment: field.endAdornment,
                    },
                  }}
                  fullWidth
                  error={!!fieldState.error}
                  helperText={
                    fieldState.error?.message ? t(String(fieldState.error.message)) : '\u00A0'
                  }
                />
              );
            }}
          />
        </>
      );
    }

    /** SELECT ----------------------------------------------------------- */
    case 'select': {
      const iconPrimary = field?.actionPrimary && !field.readonly && (
        <IconButton
          aria-label={field.actionPrimaryLabel ?? 'Action'}
          onClick={field.actionPrimary}
          disabled={field.actionPrimaryDisabled}
          size="small"
        >
          {field?.actionPrimaryIcon && <field.actionPrimaryIcon />}
        </IconButton>
      );

      const iconSecondary = field?.actionSecondary && !field.readonly && (
        <IconButton
          aria-label={field.actionSecondaryLabel ?? 'Action'}
          onClick={field.actionSecondary}
          size="small"
        >
          {field?.actionSecondaryIcon && <field.actionSecondaryIcon />}
        </IconButton>
      );
      return (
        <>
          <SelectField
            key={field.name}
            name={field.name}
            resultsOnlyWithSearch={field.resultsOnlyWithSearch}
            disableClearable={field.disableClearable}
            disabled={field.readonly || field.disabled}
            multiple={field.multiple}
            options={(field.options as FieldOption[]) ?? []}
            placeholder={placeholder}
            labelComponent={labelComponent()}
            iconPrimary={iconPrimary}
            iconSecondary={iconSecondary}
          />
        </>
      );
    }
    /** LABEL (apenas texto) -------------------------------------------- */
    case 'label':
      return (
        <>
          {labelComponent()}
          <div
            style={{
              padding: '24px 0',
              fontWeight: 500,
              fontSize: '1rem',
              marginBottom: 4,
            }}
          >
            {field?.labelValue}
          </div>
        </>
      );

    /** EMPTY ------------------------------------------------------------ */
    case 'empty':
      return <FormControl fullWidth />;

    /** CHECKBOX ---------------------------------------------------------- */
    case 'checkbox':
      return (
        <Controller
          name={field.name}
          rules={{
            required: field.required,
          }}
          control={methods.control}
          render={({ field: f, fieldState }) => (
            <>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={!!f.value}
                    onChange={(e) => f.onChange(e.target.checked)}
                    disabled={field.readonly}
                  />
                }
                label={labelComponent()}
              />
              {fieldState.error?.message && (
                <Typography color="error" variant="caption" sx={{ ml: 1 }}>
                  {fieldState.error?.message ? t(String(fieldState.error.message)) : '\u00A0'}
                </Typography>
              )}
            </>
          )}
        />
      );

    /** MULTIFIELDLINE ---------------------------------------------------------- */
    case 'multiFieldLine':
      return children ? (
        <ComposableForm.Internal.Layout label={field.label} key={'multi' + field.name}>
          {children}
        </ComposableForm.Internal.Layout>
      ) : null;

    /** ICON BUTTON --------------------------------------------------- */
    case 'iconButton':
      return (
        <IconButton
          aria-label={field.actionLabel ?? 'Action'}
          onClick={field.action}
          disabled={field.readonly}
          sx={{ marginTop: '28px' }}
          size="small"
        >
          {field.icon}
        </IconButton>
      );
    /** ICON BUTTON WITH TEXT ----------------------------------------- */
    case 'iconButtonText':
      return <IconButtonText field={field} t={t} />;

    /** FILE UPLOAD ------------------------------------------------- */
    // TODO change later on to standalone component with better styling
    // TODO this hsould spawn a modal, not a direct upload
    case 'fileUploadModal': {
      return (
        <FileUploadModal field={field} labelComponent={labelComponent}>
          {children}
        </FileUploadModal>
      );
    }

    /** FILE GROUP ------------------------------------------------------- */
    case 'fileGroup':
      return <FileCard field={field} methods={methods as UseFormReturn<Record<string, unknown>>} />;

    /** FILE UPLOAD ------------------------------------------------------- */
    case 'fileUpload':
      return (
        <FileUploadField name={field.name} disabled={field.readonly} placeholder={placeholder} />
      );

    /** DATE PICKER ------------------------------------------------------- */
    case 'date':
    case 'datetime':
    case 'time': {
      const PickerComponent =
        field.type === 'date' ? DatePicker : field.type === 'time' ? TimePicker : DateTimePicker;

      return (
        <>
          {labelComponent()}
          <Controller
            name={field.name}
            rules={{
              required: field.required,
            }}
            render={({ field: f, fieldState }) => (
              <>
                <PickerComponent
                  value={f.value ? new Date(f.value) : null}
                  onChange={(date: any) => f.onChange(date)}
                  disabled={field.readonly || field.disabled}
                  slotProps={{
                    textField: {
                      error: !!fieldState.error,
                      placeholder,
                      autoCapitalize: 'off',
                      fullWidth: true,
                      size: 'small',
                    },
                  }}
                />
                <Typography color="error" variant="caption" sx={{ ml: 1 }}>
                  {fieldState.error?.message ? t(String(fieldState.error.message)) : '\u00A0'}
                </Typography>
              </>
            )}
          />
        </>
      );
    }
    case 'phonenumber':
      return (
        <>
          {labelComponent()}
          <Controller
            name={field.name}
            rules={{
              required: field.required,
            }}
            render={({ field: f, fieldState }) => (
              <TextField
                {...f}
                type={'tel'}
                placeholder={placeholder}
                disabled={field.readonly}
                fullWidth
                multiline={field.multiline}
                minRows={field.multiline ? 4 : undefined}
                error={!!fieldState.error}
                helperText={
                  fieldState.error?.message ? t(String(fieldState.error.message)) : '\u00A0'
                }
              />
            )}
          />
        </>
      );

    case 'togglebutton':
      return (
        <>
          {labelComponent()}
          <Controller
            name={field.name}
            control={methods.control}
            render={({ field: f }) => (
              <ToggleButtonGroup
                value={f.value ? 'ativo' : 'inativo'}
                exclusive
                onChange={(event, newValue) => {
                  if (newValue !== null) {
                    f.onChange(newValue === 'ativo' ? true : false);
                  }
                }}
                aria-label="estado"
              >
                <ToggleButton
                  value="ativo"
                  sx={{
                    flex: 1,
                    width: '70px',
                    height: '40px',
                    border: '1px solid lightgray',
                    textTransform: 'none',
                    '&.Mui-selected': {
                      backgroundColor: '#E8F5E9',
                      color: '#1B5E20',
                      border: '1px solid #1B5E20',
                    },
                    '&:hover': {
                      backgroundColor: '#ffffffff',
                      borderColor: '#4b3939ff',
                    },
                    '&.Mui-selected:hover': {
                      backgroundColor: '#E8F5E9',
                    },
                  }}
                >
                  {t(field.options![0].label!)}
                </ToggleButton>
                <ToggleButton
                  value="inativo"
                  sx={{
                    flex: 1,
                    width: '70px',
                    height: '40px',
                    textTransform: 'none',
                    '&.Mui-selected': {
                      backgroundColor: '#FEEBEE',
                      color: '#B71C1C',
                      border: '1px solid #B71C1C',
                    },
                    '&:hover': {
                      backgroundColor: '#ffffffff',
                      borderColor: '#4b3939ff',
                    },
                    '&.Mui-selected:hover': {
                      backgroundColor: '#FEEBEE',
                    },
                  }}
                >
                  {t(field.options![1].label!)}
                </ToggleButton>
              </ToggleButtonGroup>
            )}
          />
        </>
      );

    case 'SearchableCheckboxList':
      return (
        <>
          {labelComponent()}
          <SearchableCheckboxList
            name={field.name}
            control={methods.control}
            items={field.items!}
            placeholder="Pesquisar polos..."
          />
        </>
      );

    /** DEFAULT (não mapeado) ------------------------------------------- */
    default:
      return null;
  }
};

export default FormField;
