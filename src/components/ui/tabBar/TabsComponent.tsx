'use client';
import { Box, Tab, Tabs } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useEffect } from 'react';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import { VIEW } from '@/constants/enums';
import { muiTheme } from '@/styles/theme';
import BaseTooltip from '@/components/form-builder/inputs/TooltipBase';
import { useTabsMethods, useTabsState } from '@/hooks/useTabs';

const getTabIcon = (tab: { view?: string }) => {
  const iconProps = { fontSize: 'small' as const, sx: { mr: 1, ml: 'auto' } };
  switch (tab.view) {
    case VIEW.VIEW:
      return <VisibilityIcon {...iconProps} />;
    case VIEW.EDIT:
    case VIEW.CREATE:
      return <EditIcon {...iconProps} />;
    default:
      return null; // or a default icon
  }
};

const TabsComponent: React.FC = () => {
  const { handleChangeTab, handleCloseTab } = useTabsMethods();
  const { openTabs } = useTabsState();

  useEffect(() => {
    console.log('opentabs are: ', openTabs);
  }, [openTabs]);
  return (
    <>
      {/* used to hide shadow from components when scroll */}
      <Box
        sx={{
          position: 'fixed',
          display: 'flex',
          left: 0,
          rigth: 0,
          top: 40,
          minHeight: 110,
          width: '100vw',
          zIndex: 2,
          backgroundColor: muiTheme.palette.background.default,
        }}
      />
      <Box
        sx={{
          position: 'sticky',
          display: 'flex',
          top: 40,
          height: 110,
          alignItems: 'flex-end',
          width: '100%',
          zIndex: 10,
          backgroundColor: muiTheme.palette.background.default,
          borderBottom: '1px solid #ccc',
        }}
      >
        <Box
          sx={{
            width: '100%',
          }}
        >
          <Tabs
            value={openTabs.findIndex((tab) => tab.isOpen)}
            onChange={(_, newValue) => handleChangeTab({ id: openTabs[newValue].id })}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              minHeight: 44,
              maxHeight: 44,
            }}
          >
            {openTabs?.map((tab) => (
              <Tab
                key={tab.id}
                label={
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {/* need to read what icon to use based on tab.view */}
                      {getTabIcon(tab)}
                      {
                        tab?.title && tab?.title?.length > 12 ? (
                          <BaseTooltip
                            label={`${tab?.title?.slice(0, 12)}...`}
                            tooltip={tab?.title}
                          />
                        ) : (
                          tab.title
                        ) /* TODO dont like this method of truncate*/
                      }
                    </Box>
                    <Box
                      component="span"
                      sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', ml: 1 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCloseTab({ id: tab.id });
                      }}
                      aria-label={`Close ${tab.title}`}
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.stopPropagation();
                          handleChangeTab({ id: tab.id });
                        }
                        if (e.key === 'Backspace') {
                          e.stopPropagation();
                          handleCloseTab({ id: tab.id });
                        }
                      }}
                    >
                      <CloseIcon fontSize="small" />
                    </Box>
                  </Box>
                }
                sx={{
                  minHeight: 40,
                  width: '180px',
                  background: tab.isOpen ? '#eee' : 'transparent',
                  //change colors after pls
                  borderRadius: '8px 8px 0 0',
                  mr: 0.5,
                  position: 'relative',
                  textTransform: 'none',
                }}
              />
            ))}
          </Tabs>
        </Box>
      </Box>
    </>
  );
};

export default TabsComponent;