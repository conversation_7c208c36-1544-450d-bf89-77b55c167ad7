'use client';

import React, { useState } from 'react';
import {
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  type SxProps,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';

export type ActionItem = {
  label: string;
  icon: React.ReactElement;
  onClick: () => void;
  color?: 'error' | 'success' | 'inherit';
  sx?: SxProps;
};

interface RowActionsProps {
  actions: ActionItem[];
}

export default function RowActions({ actions }: RowActionsProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClose = () => setAnchorEl(null);

  return (
    <>
      <IconButton size="small" onClick={(e) => setAnchorEl(e.currentTarget)}>
        <MoreVertIcon />
      </IconButton>

      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {actions.map((action, idx) => (
          <MenuItem
            key={idx}
            onClick={() => {
              action.onClick();
              handleClose();
            }}
            sx={action.sx}
          >
            <ListItemIcon>
              {React.cloneElement(action.icon as React.ReactElement<any>, {
                fontSize: 'small',
                color: action.color ?? 'inherit',
              })}
            </ListItemIcon>
            <ListItemText primary={action.label} />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
}
