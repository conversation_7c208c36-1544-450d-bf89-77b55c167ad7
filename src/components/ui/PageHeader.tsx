'use client';

import { usePathname, useParams } from 'next/navigation';
import { PAGE_HEADERS } from '@/config/pageHeaders';
import { Typography, Button, Box, Stack } from '@mui/material';
import Link from 'next/link';
import { matchPath } from '@/utils';
import { useTranslations } from 'next-intl';
import SplitMenuButton from './SplitMenuButton';
import usePageHeaderStore from '@/store/PageHeader';

function resolvePathVars(path: string, vars: Record<string, string | number>) {
  return Object.entries(vars).reduce(
    (acc, [key, val]) => acc.replace(new RegExp(`:${key}\\b`, 'g'), String(val)),
    path
  );
}

export type MenuHandlerMap = Record<string, () => void>;

const stripTrailingSlash = (s: string) => (s.endsWith('/') && s !== '/' ? s.slice(0, -1) : s);

export default function PageHeader() {
  const t = useTranslations('dashboard');
  const raw = usePathname() || '';
  const params = useParams<Record<string, string>>();
  const clean = stripTrailingSlash(raw.replace(/^\/[a-z]{2}(?:-[A-Z]{2})?/, ''));

  const headers = PAGE_HEADERS();
  const found =
    headers.find(([pattern]) => !pattern.includes(':') && stripTrailingSlash(pattern) === clean) ??
    headers.find(
      ([pattern]) => pattern.includes(':') && matchPath(stripTrailingSlash(pattern), clean)
    );

  const handlers = usePageHeaderStore((s) => s.handlers);

  const resolveItem = (item: any) => ({
    ...item,
    onClick:
      typeof item.onClick === 'string'
        ? handlers[item.onClick] ?? (() => console.warn(`Sem handler: ${item.onClick}`))
        : item.onClick,
  });
  const config = found?.[1];
  if (!config) return null;

  return (
    <Box display="flex" alignItems="center" justifyContent="space-between" mb={3} gap={2}>
      <Box>
        <Typography variant="h5">{t(config.title)}</Typography>
        {config.subtitle && (
          <Typography variant="body1" color="text.secondary">
            {t(config.subtitle)}
          </Typography>
        )}
      </Box>
      <Stack direction="row" spacing={2}>
        {config?.secondaryAction?.href && (
          <Button
            component={Link}
            variant="outlined"
            href={resolvePathVars(config.secondaryAction.href, params)}
            startIcon={config.secondaryAction.icon?.()}
          >
            {t(config.secondaryAction.label)}
          </Button>
        )}
        {config.action &&
          Object.keys(config.action).length > 0 &&
          (config.action.menu ? (
            <SplitMenuButton
              label={t(config.action.label)}
              items={config.action.menu.map(resolveItem)}
            />
          ) : (
            config?.action?.href && (
              <Button
                component={Link}
                href={resolvePathVars(config.action.href, params)}
                variant="contained"
                startIcon={config.action.icon?.()}
              >
                {t(config.action.label)}
              </Button>
            )
          ))}
      </Stack>
      {config.actions && <Box>{config.actions}</Box>}
    </Box>
  );
}
