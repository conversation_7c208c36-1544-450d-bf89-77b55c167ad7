import {
  Box,
  Breakpoint,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import { identity } from 'lodash';
import { useTranslations } from 'next-intl';
import { ReactNode } from 'react';

interface FormBuilderDialogProps {
  children: ReactNode;
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  title: string;
  maxWidth?: Breakpoint | false;
  cancelLabel?: string;
  confirmLabel?: string;
  confirmColor?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  showActions?: boolean;
  confirmIcon?: ReactNode;
}
export default function FormBuilderDialog({
  open,
  onCancel,
  onConfirm,
  children,
  title,
  maxWidth,
  showActions = true,
  cancelLabel = 'dashboard.crm.leads.lead.details.forms.buttons.cancel',
  confirmLabel = 'dashboard.crm.leads.lead.details.forms.buttons.confirm',
  confirmColor = 'primary',
  confirmIcon,
}: FormBuilderDialogProps) {
  const t = useTranslations();

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="formBuilder-dialog-title"
      aria-describedby="formBuilder-dialog-description"
      fullWidth
      maxWidth={maxWidth}
    >
      <DialogTitle id="formBuilder-dialog-title">{t(title)}</DialogTitle>
      <DialogContent dividers>{children}</DialogContent>

      <DialogActions sx={{ px: 3, p: 2 }}>
        {showActions && (
          <>
            <Button onClick={onCancel} variant="outlined">
              {t(cancelLabel)}
            </Button>

            <Button
              startIcon={confirmIcon}
              onClick={onConfirm}
              variant="contained"
              color={confirmColor}
              autoFocus
            >
              {t(confirmLabel)}
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
}

export function LoadingDialog({
  title = '',
  maxWidth,
  onCancel = identity,
}: {
  title?: string;
  maxWidth?: Breakpoint | false;
  onCancel?: () => any;
}) {
  return (
    <FormBuilderDialog
      title={title}
      maxWidth={maxWidth}
      open
      showActions={false}
      onCancel={onCancel}
      onConfirm={identity}
    >
      <Box
        width={'100%'}
        display={'flex'}
        justifyContent={'center'}
        alignItems={'center'}
        overflow={'hidden'}
      >
        <CircularProgress size={100} color="inherit" />
      </Box>
    </FormBuilderDialog>
  );
}
