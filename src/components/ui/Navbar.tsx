'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>l<PERSON>,
  Box,
  IconButton,
  Button,
  ButtonGroup,
  Divider,
  Avatar,
  Typography,
  MenuItem,
  ListItemIcon,
  Menu,
  Stack,
} from '@mui/material';
import {
  NotificationsRounded,
  Menu as MenuIcon,
  Setting<PERSON>,
  Person,
  Logout,
  AccountCircle,
} from '@mui/icons-material';
import { useSession, signOut } from 'next-auth/react';
import { useIsMobile } from '@/hooks/useIsMobile';
import { useLocale, useTranslations } from 'next-intl';
import { useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { Link as IntlLink } from '@/i18n/navigation';
import Link from 'next/link';

type Props = {
  onDrawerToggle?: () => void;
};

export default function Navbar({ onDrawerToggle }: Props) {
  const { data: session } = useSession();
  const user = session?.user;
  const loggedIn = Boolean(user);

  const isMobile = useIsMobile();
  const locale = useLocale();
  const t = useTranslations();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);

  const pathname = usePathname();
  const searchParams = useSearchParams().toString();
  const [, , ...segments] = pathname.split('/');
  const pathWithoutLocale = '/' + segments.join('/');
  const queryString = searchParams.toString();
  const href = pathWithoutLocale + (queryString ? `?${queryString}` : '');

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/sign-in' });
  };

  const initials =
    user?.name
      ?.split(' ')
      .map((n) => n[0])
      .join('')
      .slice(0, 2)
      .toUpperCase() ?? '';

  return (
    <AppBar
      position="fixed"
      elevation={0}
      sx={(theme) => ({
        backgroundColor: theme.palette.background.default,
        borderBottom: `1px solid ${theme.palette.divider}`,
      })}
    >
      <Toolbar
        sx={{
          px: { xs: 2, sm: 4, md: 8 },
          height: 64,
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {onDrawerToggle && (
            <IconButton edge="start" onClick={onDrawerToggle} sx={{ display: { sm: 'none' } }}>
              <MenuIcon fontSize="large" />
            </IconButton>
          )}
        </Box>
        {loggedIn ? (
          <Stack direction="row" gap={2}>
            <Box sx={{ flexGrow: 1 }} />
            <Box sx={{ px: 2, py: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <ButtonGroup variant="text">
                {['en', 'pt'].map((lng) => (
                  <IntlLink key={lng} href={href} locale={lng}>
                    <Button
                      size="medium"
                      sx={{
                        color: locale === lng ? 'primary.main' : 'text.primary',
                      }}
                    >
                      <Typography variant="buttonMedium">{lng.toUpperCase()}</Typography>
                    </Button>
                  </IntlLink>
                ))}
              </ButtonGroup>

              <IconButton size="small">
                <NotificationsRounded />
              </IconButton>
              <Divider orientation="vertical" variant="middle" flexItem sx={{ mx: 2 }} />
              <Box>
                <Typography variant="body1">{user?.name}</Typography>
                <Typography variant="subtitle2">Administrador</Typography>
              </Box>
              <IconButton
                size="large"
                edge="end"
                aria-label="account of current user"
                aria-controls="menu-appbar"
                aria-haspopup="true"
                color="inherit"
                onClick={handleMenuOpen}
              >
                <AccountCircle />
              </IconButton>
              <Menu
                id="account-menu"
                anchorEl={anchorEl}
                open={menuOpen}
                onClose={handleMenuClose}
                onClick={handleMenuClose}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                slotProps={{ paper: { elevation: 4, sx: { mt: 1.5, minWidth: 200 } } }}
              >
                <Box sx={{ px: 2, py: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar>{initials}</Avatar>
                  <Box>
                    <Typography variant="subtitle1">{user?.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {user?.email}
                    </Typography>
                  </Box>
                </Box>
                <Divider />

                <MenuItem
                  onClick={() => {
                    /* navegar para settings */
                  }}
                >
                  <ListItemIcon>
                    <Settings fontSize="small" />
                  </ListItemIcon>
                  Settings
                </MenuItem>

                <MenuItem
                  onClick={() => {
                    /* navegar para profile */
                  }}
                >
                  <ListItemIcon>
                    <Person fontSize="small" />
                  </ListItemIcon>
                  Profile
                </MenuItem>

                <Divider />

                <MenuItem onClick={handleSignOut}>
                  <ListItemIcon>
                    <Logout fontSize="small" />
                  </ListItemIcon>
                  Sign out
                </MenuItem>
              </Menu>
            </Box>
          </Stack>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ButtonGroup variant="text">
              {['en', 'pt'].map((lng) => (
                <IntlLink key={lng} href={href} locale={lng}>
                  <Button
                    size="medium"
                    sx={{
                      color: locale === lng ? 'primary.main' : 'text.buttonMedium',
                    }}
                  >
                    <Typography variant="buttonMedium">{lng.toUpperCase()}</Typography>
                  </Button>
                </IntlLink>
              ))}
            </ButtonGroup>

            <Button component={Link} href="/sign-in" variant="text">
              {isMobile ? t('signIn.mobileTitle') : t('signIn.title')}
            </Button>
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
}
