import { KeyboardArrowDownRounded } from '@mui/icons-material';
import { Button, ButtonGroup, Link, Menu, MenuItem, useTheme } from '@mui/material';
import { useState } from 'react';

export default function SplitMenuButton({
  label,
  items,
}: {
  label: string;
  items: Array<{
    label: string;
    icon?: () => React.ReactNode;
    href?: string;
    onClick?: () => void;
  }>;
}) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const theme = useTheme();

  return (
    <>
      <ButtonGroup
        variant="contained"
        disableElevation
        color="primary"
        sx={{
          '&:hover .MuiButtonGroup-grouped': {
            backgroundColor: theme.palette.primary.dark,
          },
          '&:hover .MuiButtonGroup-grouped:not(:last-of-type)': {
            borderRight: `1px solid ${theme.palette.primary.main}`,
          },
        }}
      >
        <Button onClick={(e) => setAnchorEl(e.currentTarget)}>{label}</Button>
        <Button color="primary" onClick={(e) => setAnchorEl(e.currentTarget)}>
          <KeyboardArrowDownRounded fontSize="small" />
        </Button>
      </ButtonGroup>

      <Menu color="primary.main" anchorEl={anchorEl} open={open} onClose={() => setAnchorEl(null)}>
        {items.map((it) => {
          const content = (
            <>
              {it.icon?.()}
              {it.label}
            </>
          );

          return it.href ? (
            <MenuItem
              key={it.label}
              component={Link}
              href={it.href}
              onClick={() => setAnchorEl(null)}
            >
              {content}
            </MenuItem>
          ) : (
            <MenuItem
              key={it.label}
              onClick={() => {
                it.onClick?.();
                setAnchorEl(null);
              }}
            >
              {content}
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
}
