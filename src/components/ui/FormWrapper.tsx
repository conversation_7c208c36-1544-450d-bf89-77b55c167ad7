import { Box } from '@mui/material';
import { ReactNode } from 'react';
import { VIEW } from '@/constants/enums';
import { ComposableForm } from '../form-builder/ComposableFormBuilder';
import FormActions from './FormActions';

type FormProps = {
  readonly children: ReactNode;
  readonly onReset?: React.FormEventHandler<HTMLFormElement>;
  readonly onSubmit?: React.FormEventHandler<HTMLFormElement>;
  readonly methods: import('react-hook-form').UseFormReturn<any>;
  readonly view: VIEW;
  readonly columns?: 1 | 2 | 3 | 4;
  readonly footer?: boolean;
};

// create a form instance with this, creating buttons on the footer of the page
export default function ComposableFormWrapper({
  children,
  onReset,
  onSubmit,
  view,
  methods,
  columns = 1,
  footer = true,
}: FormProps) {
  return (
    <Box sx={{ display: 'contents' }} component="form" onReset={onReset} onSubmit={onSubmit}>
      <ComposableForm.Provider columns={columns} methods={methods}>
        {children}
        {footer && <FormActions view={view} />}
      </ComposableForm.Provider>
    </Box>
  );
}
