'use client';

import { Card, CardContent, Tab, Tabs } from '@mui/material';
import { identity } from 'lodash';
import { useTranslations } from 'next-intl';
import { ReactNode, useMemo } from 'react';

export type SectionTab = { label: string; value: number | string };

export interface SectionCardInternalProps<T extends string | number> {
  headerColor?: string;
  sectionColor?: string;
  children: ReactNode;
  tabs: SectionTab[];
  addSpacer?: boolean;
  selectedTab: T;
  onSelectTab: (value: T) => any;
}

export interface SectionCardProps {
  children: ReactNode;
  addSpacer?: boolean;
  title: string;
  headerColor?: string;
  sectionColor?: string;
}

//addSpacer is necessary when you are on a page that includes footer actions
export function SectionCardInternal<T extends string | number>({
  headerColor,
  children,
  addSpacer = false,
  tabs,
  selectedTab,
  onSelectTab,
}: SectionCardInternalProps<T>) {
  const t = useTranslations();

  return (
    <>
      <Card sx={{ width: '100%', marginBottom: addSpacer ? '72px' : undefined }}>
        <CardContent
          sx={{
            py: 2,
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '1rem',
          }}
        >
          <Tabs
            value={selectedTab}
            onChange={(_, val) => onSelectTab(val)}
            sx={{
              borderBottom: 1,
              mb: 2,
              borderColor: headerColor ?? 'divider',
              width: '100%',
            }}
            slotProps={{
              indicator: {
                sx: {
                  mt: 1,
                  width: tabs.length === 1 ? '100% !important' : undefined,
                },
              },
            }}
          >
            {tabs.map((tab) => (
              <Tab
                key={tab.value}
                sx={{ textTransform: 'none' }}
                label={t(tab.label)}
                value={tab.value}
              />
            ))}
          </Tabs>
          {children}
        </CardContent>
      </Card>
    </>
  );
}

export default function SectionCard({
  title,
  addSpacer,
  children,
  sectionColor,
}: SectionCardProps) {
  const tabs = useMemo<SectionTab[]>(() => [{ label: title, value: 0 }], [title]);

  return (
    <SectionCardInternal
      sectionColor={sectionColor}
      addSpacer={addSpacer}
      tabs={tabs}
      selectedTab={0}
      onSelectTab={identity}
    >
      {children}
    </SectionCardInternal>
  );
}
