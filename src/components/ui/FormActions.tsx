'use client';

import { VIEW } from '@/constants/enums';
import { useTabsMethods } from '@/hooks/useTabs';
import { muiTheme } from '@/styles/theme';
import { Box, Button, Stack } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';

export default function FormActions({
  view,
  actionLabel = 'forms.label.save',
  secondActionLabel = 'forms.label.edit',
  secondaryActionLabel = 'forms.label.cancel',
}: {
  view: VIEW;
  actionLabel?: string;
  secondActionLabel?: string;
  secondaryActionLabel?: string;
}) {
  const t = useTranslations();
  const { formState } = useFormContext();
  const { openTab, handleUpdateTabView, handleCloseTab } = useTabsMethods();

  const showSecondaryAction = view !== VIEW.VIEW;
  const shouldCloseTab = view === VIEW.CREATE;

  const disabled =
    /*!formState.isDirty || formState.disabled || !!formState.errors.length || */ !formState.isValid;

  return (
    <Box
      sx={{
        width: '100%',
        zIndex: 2,
        backgroundColor: muiTheme.palette.background.default,
        position: 'fixed',
        paddingRight: '20px',
        right: 0,
        bottom: 0,
        left: 0,
        height: 74,
        boxShadow: muiTheme.shadows[5],
      }}
      display="flex"
      alignItems="center"
      justifyContent="flex-end"
      gap={2}
    >
      <Stack direction="row" spacing={2}>
        {showSecondaryAction && (
          <Button
            type="reset"
            variant="outlined"
            onClick={() => {
              if (shouldCloseTab) {
                handleCloseTab({ id: openTab()?.id ?? '' });
              } else {
                handleUpdateTabView({ view: VIEW.VIEW });
              }
            }}
          >
            {t(secondaryActionLabel)}
          </Button>
        )}
        {view === VIEW.VIEW && (
          <Button
            variant="contained"
            startIcon={<EditIcon />}
            onClick={() => handleUpdateTabView({ view: VIEW.EDIT })}
          >
            {t(secondActionLabel)}
          </Button>
        )}
        {view !== VIEW.VIEW && (
          <Button type="submit" variant="contained" startIcon={<SaveIcon />} disabled={disabled}>
            {t(actionLabel)}
          </Button>
        )}
      </Stack>
    </Box>
  );
}
