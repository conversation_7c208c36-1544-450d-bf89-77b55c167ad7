'use client';

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Typo<PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>ert,
  CircularProgress,
} from '@mui/material';
import { recoverPasswordAction } from '@/app/actions/auth';
import type { RecoverPasswordState } from '@/app/actions/auth';
import { useActionState, useEffect, useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export default function RecoverPasswordForm() {
  const [email, setEmail] = useState('');
  const [state, formAction] = useActionState<RecoverPasswordState, FormData>(
    recoverPasswordAction,
    { errors: {} }
  );
  const router = useRouter();
  const locale = useLocale();

  const t = useTranslations();

  useEffect(() => {
    if (state.success) {
      router.push(`/${locale}/forgot-password/sent?email=${encodeURIComponent(email)}`);
    }
  }, [state.success, router]);

  return (
    <Container maxWidth="sm" sx={{ py: 4 }}>
      <Card>
        <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5" textAlign="center">
            {t('forgotPassword.title')}
          </Typography>

          {state.errors?.generic && <Alert severity="error">{state.errors.generic}</Alert>}

          <Box
            component="form"
            action={formAction}
            noValidate
            sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
          >
            <input type="hidden" name="languageCode" value={locale} />
            <TextField
              name="email"
              label={t('form.email')}
              type="email"
              fullWidth
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={!!state.errors?.email}
              helperText={state.errors?.email}
              disabled={state.pending || state.success}
            />

            <Button
              type="submit"
              variant="contained"
              fullWidth
              disabled={state.pending || state.success}
              startIcon={state.pending ? <CircularProgress size={20} color="inherit" /> : undefined}
            >
              {state.pending
                ? t('button.submitting')
                : state.success
                ? t('button.sent')
                : t('button.sendReset')}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
