import { z } from 'zod';
import { QueryClient } from '@tanstack/react-query';
import type { FieldOption } from '../components/form-builder/types/formBuilder';
import { cache } from 'react';
import { identity } from 'lodash';

/**
 * Cleans an object by removing null, undefined, and empty strings.
 *
 * @param {Object} obj - The object to clean.
 * @returns {Object} - The cleaned object.
 */
export function cleanObject<T extends Record<string, any>>(obj: T): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([, value]) => {
      if (value === null || value === undefined) return false;
      if (typeof value === 'string') return value.trim() !== '';
      return true;
    })
  ) as Partial<T>;
}

export const ipadMaxSize = 1180; //ipad air

export const isEmptyField = (value: string | null | undefined) =>
  [null, undefined, ''].includes(value);

export const isSubRoute = (currentPath: string, basePath: string): boolean => {
  return currentPath === basePath || currentPath.startsWith(`${basePath}/`);
};

type Result<T, E = Error> = { response: T; error: null } | { response: null; error: E };

export async function tryCatch<T, E = Error>(promise: Promise<T>): Promise<Result<T, E>> {
  try {
    const response = await promise;
    return { response, error: null };
  } catch (error) {
    return { response: null, error: error as E };
  }
}

export const findTabByPath = (groups: any, path: any) => {
  for (const group of groups) {
    for (const section of group.sections) {
      if (section.href === path) return section;
      if (section.items) {
        const item = section.items.find((navItem: any) => navItem.href === path);
        if (item) return item;
      }
    }
  }
  return null;
};

export function matchPath(pattern: string, path: string): boolean {
  const regexString =
    '^' +
    pattern
      .replace(/\/$/, '')
      .replace(/:[^/]+/g, '[^/]+')
      .replace(/\//g, '\\/') +
    '$';
  return new RegExp(regexString).test(path);
}

export const pathWithoutLocale = (pathname: string): string => {
  return pathname.replace(/^\/(pt|en)(?=\/|$)/, '') || '/';
};
export const pathWithoutLocaleAndId = (pathname: string): string => {
  const base = pathWithoutLocale(pathname);
  return base.replace(/\/[^/]+$/, '') || '/';
};
export const pathParentFinale = (pathname: string): string => {
  return pathWithoutLocaleAndId(pathname).split('/').pop() ?? 'record';
};
export function emptyAsUndefined(x: any) {
  return x ? x : undefined;
}

export const run: <T>(fn: () => T) => T = (fn) => fn();

//for testing purposes only
export const sleep = (millis: number): Promise<any> =>
  new Promise((resolve) =>
    setTimeout(async () => resolve(undefined), process.env.NODE_ENV === 'production' ? 0 : millis)
  );
export const pathParentFinaleNew = (pathname: string): string => {
  const parts = pathWithoutLocale(pathname).split('/');
  const newIndex = parts.lastIndexOf('new');
  return newIndex > 0 ? parts[newIndex - 1] : 'record';
};

// used to initialize fields with default values that are not optional, so zod shoots the correct error on new forms
export function getDefaultValuesWithInitial(
  schema: z.ZodObject<any>,
  initial: Record<string, any> = {}
) {
  const isOnlyId = Object.keys(initial).length === 1 && 'id' in initial;
  if (isOnlyId) {
    const defaults = Object.keys(schema.shape).reduce((acc, key) => {
      const field = schema.shape[key];
      // Only set default if field is not optional
      if (!field.isOptional()) {
        acc[key] = '';
      }
      return acc;
    }, {} as Record<string, any>);
    return { ...defaults, id: initial.id };
  }
  return initial;
}

export const mapLabel = <T extends string>(
  x: Readonly<T[]>,
  formatLabel: (x: T) => string = identity
): FieldOption[] =>
  x.map((x) => ({
    label: formatLabel(x),
    value: x,
  }));

export const getQueryClient = cache(
  () =>
    new QueryClient({
      defaultOptions: {
        queries: {
          queryFn: () => {
            console.log('defaulted...');
            return null;
          },
          refetchOnMount: false,
          refetchOnReconnect: false,
          refetchOnWindowFocus: false,
          throwOnError: true,
          // keep data fresh for 5 minutes
          staleTime: 5 * 60 * 1000,
          // retry failed requests once
          retry: 1,
        },
      },
    })
);
