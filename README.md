````md
# Caixa Mágica

A battle‑tested boilerplate for modern **Next.js 15** applications.

| Core                                                            | DX / UX                                     | Ops |
| --------------------------------------------------------------- | ------------------------------------------- | --- |
| ✅ Next.js 15 (App Router + Server Components + Server Actions) | 🐳 Docker multi‑stage build                 |
| 🔐 NextAuth.js with JWT & Role‑Based Access Control             | 🌐 next‑intl i18n routing (`/pt`, `/en`, …) |     |
| 🎨 Material UI v6 (MUI) + optional Tailwind utilities           |

## MUI

## Table of Contents

- [Features](#features)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Environment Variables](#environment-variables)
  - [Running Locally](#running-locally)
  - [Running with Docker](#running-with-docker)
- [Internationalization](#internationalization)
- [Authentication & RBAC](#authentication--rbac)
- [Project Structure](#project-structure)
- [Scripts](#scripts)
- [License](#license)

---

## Features

- **Next.js 15** App Router architecture (Route Groups, Middleware, Server Actions)
- **Server Components** + granular **Client Components**
- **Material UI v6** theme with Emotion; Tailwind available for utility classes
- **AuthN & RBAC**
  - NextAuth.js (Credentials provider)
  - JWT strategy with `session.user.roles`
  - Central `can(userRoles, permission)` helper
  - `<AuthGuard>` & `<Permissioned>` components
  - Edge Middleware for URL‑level protection
- **Flat JSON Permission List** (`/src/data/permissions.json`)
  - One source of truth; no giant admin payloads
- **i18n** via next‑intl and `[locale]` route segment
- **Dockerized** production build (multi‑stage)

---

## Getting Started

### Prerequisites

- Node.js ≥ 20
- npm / pnpm / Yarn
- Docker (optional for container builds)

### Environment Variables

```bash
cp .env.example .env.local
```
````

Then set:

```bash
NEXTAUTH_SECRET=your-secret
API_BASE_URL=https://api.yourdomain.com
```

> **Note**: you may have additional variables depending on your API.

### Running Locally

```bash
npm install
npm run dev
# http://localhost:3000/pt  (or /en)
```

### Running with Docker

```bash
docker build -t caixamagica .
docker run -d --name caixamagica --env-file .env.local -p 3000:3000 caixamagica
```

Your app will be available at [http://localhost:3000/pt](http://localhost:3000/pt) (or /en).

---

## Internationalization

This project uses **next-intl**. Translations live under:

```
public/locales/
├─ en.json
└─ pt.json
```

- Your **app** directory is nested under `/app/[locale]/…`, so that building generates both `/pt` and `/en` routes.

- In your Server‐side layout (`app/[locale]/layout.tsx`) you call:

  ```ts
  import { getLocale, getMessages } from 'next-intl/server';
  …
  const locale   = await getLocale();
  const messages = await getMessages({ locale });
  ```

- In **client** components (e.g. pages, Navbar) use:

  ```ts
  import { useTranslations, useLocale } from 'next-intl';
  const t = useTranslations();
  const locale = useLocale();
  ```

- To switch languages, use the `Link` component from `next-intl`:

  ```tsx
  import { Link } from 'next-intl';

  <Link href={…} locale="en">EN</Link>
  <Link href={…} locale="pt">PT</Link>
  ```

---

## Authentication / RBAC

- NextAuth.js is configured in `app/api/auth/[...nextauth]/route.ts`.
- Credentials provider for email/password
- JWT session strategy + custom `jwt` and `session` callbacks inject roles.
- Middleware (`middleware.ts`) uses:

  ```ts
  import { withAuth } from 'next-auth/middleware';

  export default withAuth({
    callbacks: {
      authorized({ token }) {
        // only allow roles.includes('admin'), etc.
        return token.roles?.includes('admin');
      },
    },
  });
  export const config = { matcher: ['/protected/:path*'] };
  ```

  | Layer                                 | What happens                                                    |
  | ------------------------------------- | --------------------------------------------------------------- |
  | **NextAuth.js**                       | Credentials login → returns JWT with `roles: string[]`          |
  | **/lib/auth/roles.ts**                | Role → `[permissionCode]` or `'*'`                              |
  | **/lib/auth/ability.ts**              | `can(userRoles, permission)` resolver                           |
  | **Edge Middleware** (`middleware.ts`) | Blocks/redirects unauthorised URLs                              |
  | **Server Components / Actions**       | `const session = await auth(); if (!can(...)) redirect('/403')` |
  | **Client Components**                 | `<Permissioned perm="LEADS_EXPORT_EXCEL">…</Permissioned>`      |

---

## Project Structure

```
src
├─ app/
│  ├─ middleware.ts              # Edge RBAC + i18n
│  │
│  ├─ api/                       # Route Handlers (auth, etc.)
│  │   └─ auth/[...nextauth]/route.ts
│  │
│  ├─ actions/                   # Server Actions (can be imported client‑side)
│  │   └─ getProfiles.ts
│  │
│  └─ [locale]/                  # i18n segment (pt, en, …)
│      ├─ layout.tsx             # I18nProvider (server)
│      ├─ providers.tsx          # Theme + Session Provider (“use client”)
│      │
│      ├─ (public)/              # No auth required
│      │   ├─ sign-in/page.tsx
│      │   ├─ sign-up/page.tsx
│      │   └─ forgot-password/page.tsx
│      │
│      └─ (protected)/           # Auth & RBAC guarded
│          ├─ layout.tsx         # <AuthGuard> wrapper
│          ├─ page.tsx           # Dashboard
│          ├─ leads/page.tsx
│          ├─ opportunities/page.tsx
│          └─ settings/page.tsx
│
├─ components/
│  ├─ auth/                      # AuthGuard, Permissioned
│  ├─ ui/                        # Design‑system atoms (Button, Card…)
│  └─ shared/                    # Domain widgets (Tables, Forms…)
│
├─ hooks/                        # useCan, useProfiles, …
│
├─ lib/
│  ├─ auth/
│  │   ├─ permissions.ts         # list/union type from JSON
│  │   ├─ roles.ts               # role matrix
│  │   └─ ability.ts             # can()
│  └─ utils/                     # humanizePermission, helpers
│
├─ data/
│  └─ permissions.json           # flat codes array
│
├─ styles/                       # globals.css, MUI theme
├─ public/locales/               # en.json, pt.json
├─ Dockerfile
└─ next.config.js
```

---

## Scripts

| Script           | Description                              |
| ---------------- | ---------------------------------------- |
| `npm run dev`    | Start dev server on port 3000            |
| `npm run build`  | Build for production (`.next/`)          |
| `npm start`      | Launch built app (`NODE_ENV=production`) |
| `npm run lint`   | ESLint static analysis                   |
| `npm run format` | Format code with Prettier                |

---
