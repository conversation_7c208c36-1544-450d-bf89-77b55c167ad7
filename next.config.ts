import { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  reactStrictMode: true,

  // 1) experimental CSS-in-JS optimizer
  experimental: {
    optimizeCss: true,
    useCache: true,
    cacheLife: {
      forever: {
        stale: Infinity,
      },
    },
  },
  // 2) (optional) server-components by default
  // in Next 13+ this is already on; only needed if you’d turned it off:
  compiler: {
    // Enables some extra SWC optimizations
    reactRemoveProperties: true,
  },

  // 3) image domains / formats (for next/image)
  images: {
    domains: ['your-cdn.com', 'images.unsplash.com'],
    formats: ['image/avif', 'image/webp'],
  },
};

const intlWrapped = withNextIntl(nextConfig);

const config = {
  ...intlWrapped,
  assetPrefix: '/',
};

export default config;
